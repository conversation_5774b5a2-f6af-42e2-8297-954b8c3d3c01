/**
 * Script pour créer les collections de vérification des certificats dans Appwrite
 * 
 * Usage: npx tsx scripts/create-verification-collections.ts
 */

import { Client, Databases, ID, IndexType } from "node-appwrite";

// Configuration Appwrite
const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT!)
  .setKey(process.env.APPWRITE_API_KEY!);

const databases = new Databases(client);
const DATABASE_ID = process.env.APPWRITE_DATABASE_ID!;

// Collection IDs
const CERTIFICATE_VERIFICATIONS_COLLECTION_ID = "certificate_verifications";
const CERTIFICATE_METADATA_COLLECTION_ID = "certificate_metadata";

async function createVerificationCollections() {
  console.log("🚀 Création des collections de vérification des certificats...");

  try {
    // 1. Créer la collection certificate_verifications
    console.log("\n📋 Création de la collection certificate_verifications...");
    
    const verificationsCollection = await databases.createCollection(
      DATABASE_ID,
      CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
      "Certificate Verifications"
    );

    console.log("✅ Collection certificate_verifications créée:", verificationsCollection.$id);

    // Ajouter les attributs pour certificate_verifications
    const verificationAttributes = [
      { key: "hash", type: "string", size: 32, required: true },
      { key: "certificateId", type: "string", size: 255, required: true },
      { key: "citizenId", type: "string", size: 255, required: true },
      { key: "issuedAt", type: "string", size: 255, required: true },
      { key: "expiresAt", type: "string", size: 255, required: true },
      { key: "isValid", type: "string", size: 10, required: true },
      { key: "isRevoked", type: "string", size: 10, required: true },
      { key: "verificationCount", type: "string", size: 20, required: true },
      { key: "lastVerifiedAt", type: "string", size: 255, required: false },
      { key: "metadataId", type: "string", size: 255, required: true },
      { key: "createdAt", type: "string", size: 255, required: true },
      { key: "updatedAt", type: "string", size: 255, required: true },
    ];

    for (const attr of verificationAttributes) {
      await databases.createStringAttribute(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        attr.key,
        attr.size,
        attr.required
      );
      console.log(`  ✅ Attribut ${attr.key} ajouté`);
    }

    // Ajouter les index pour certificate_verifications
    const verificationIndexes = [
      { key: "hash_unique", type: IndexType.Unique, attributes: ["hash"] },
      { key: "certificate_id", type: IndexType.Key, attributes: ["certificateId"] },
      { key: "citizen_id", type: IndexType.Key, attributes: ["citizenId"] },
      { key: "metadata_id", type: IndexType.Key, attributes: ["metadataId"] },
      { key: "expires_at", type: IndexType.Key, attributes: ["expiresAt"] },
      { key: "status", type: IndexType.Key, attributes: ["isValid", "isRevoked"] },
    ];

    for (const index of verificationIndexes) {
      await databases.createIndex(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        index.key,
        index.type,
        index.attributes
      );
      console.log(`  ✅ Index ${index.key} ajouté`);
    }

    // 2. Créer la collection certificate_metadata
    console.log("\n📋 Création de la collection certificate_metadata...");
    
    const metadataCollection = await databases.createCollection(
      DATABASE_ID,
      CERTIFICATE_METADATA_COLLECTION_ID,
      "Certificate Metadata"
    );

    console.log("✅ Collection certificate_metadata créée:", metadataCollection.$id);

    // Ajouter les attributs pour certificate_metadata
    const metadataAttributes = [
      { key: "verificationId", type: "string", size: 255, required: true },
      { key: "issuerType", type: "string", size: 50, required: true },
      { key: "issuerId", type: "string", size: 255, required: true },
      { key: "issuerName", type: "string", size: 255, required: true },
      { key: "region", type: "string", size: 100, required: true },
      { key: "commune", type: "string", size: 100, required: true },
      { key: "quartier", type: "string", size: 100, required: true },
      { key: "revocationReason", type: "string", size: 500, required: false },
      { key: "revokedAt", type: "string", size: 255, required: false },
      { key: "revokedBy", type: "string", size: 255, required: false },
      { key: "createdAt", type: "string", size: 255, required: true },
      { key: "updatedAt", type: "string", size: 255, required: true },
    ];

    for (const attr of metadataAttributes) {
      await databases.createStringAttribute(
        DATABASE_ID,
        CERTIFICATE_METADATA_COLLECTION_ID,
        attr.key,
        attr.size,
        attr.required
      );
      console.log(`  ✅ Attribut ${attr.key} ajouté`);
    }

    // Ajouter les index pour certificate_metadata
    const metadataIndexes = [
      { key: "verification_id", type: IndexType.Unique, attributes: ["verificationId"] },
      { key: "issuer", type: IndexType.Key, attributes: ["issuerType", "issuerId"] },
      { key: "location", type: IndexType.Key, attributes: ["region", "commune", "quartier"] },
      { key: "issuer_name_fulltext", type: IndexType.Fulltext, attributes: ["issuerName"] },
    ];

    for (const index of metadataIndexes) {
      await databases.createIndex(
        DATABASE_ID,
        CERTIFICATE_METADATA_COLLECTION_ID,
        index.key,
        index.type,
        index.attributes
      );
      console.log(`  ✅ Index ${index.key} ajouté`);
    }

    console.log("\n🎉 Collections de vérification créées avec succès !");
    console.log("\n📊 Résumé:");
    console.log(`  ✅ ${CERTIFICATE_VERIFICATIONS_COLLECTION_ID} - ${verificationAttributes.length} attributs, ${verificationIndexes.length} index`);
    console.log(`  ✅ ${CERTIFICATE_METADATA_COLLECTION_ID} - ${metadataAttributes.length} attributs, ${metadataIndexes.length} index`);

  } catch (error) {
    console.error("❌ Erreur lors de la création des collections:", error);
    process.exit(1);
  }
}

async function checkCollectionsExist() {
  try {
    console.log("🔍 Vérification de l'existence des collections...");
    
    const collections = await databases.listCollections(DATABASE_ID);
    const existingCollections = collections.documents.map(col => col.$id);
    
    const verificationsExists = existingCollections.includes(CERTIFICATE_VERIFICATIONS_COLLECTION_ID);
    const metadataExists = existingCollections.includes(CERTIFICATE_METADATA_COLLECTION_ID);
    
    if (verificationsExists || metadataExists) {
      console.log("⚠️  Certaines collections existent déjà:");
      if (verificationsExists) console.log(`  - ${CERTIFICATE_VERIFICATIONS_COLLECTION_ID}`);
      if (metadataExists) console.log(`  - ${CERTIFICATE_METADATA_COLLECTION_ID}`);
      
      console.log("\n❓ Voulez-vous continuer ? (les collections existantes seront ignorées)");
      return false; // En production, vous pourriez vouloir demander confirmation
    }
    
    return true;
  } catch (error) {
    console.error("❌ Erreur lors de la vérification:", error);
    return false;
  }
}

async function main() {
  console.log("🔐 Script de création des collections de vérification");
  console.log("=" .repeat(60));
  
  // Vérifier les variables d'environnement
  if (!process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 
      !process.env.NEXT_PUBLIC_APPWRITE_PROJECT || 
      !process.env.APPWRITE_API_KEY || 
      !process.env.APPWRITE_DATABASE_ID) {
    console.error("❌ Variables d'environnement manquantes:");
    console.error("  - NEXT_PUBLIC_APPWRITE_ENDPOINT");
    console.error("  - NEXT_PUBLIC_APPWRITE_PROJECT");
    console.error("  - APPWRITE_API_KEY");
    console.error("  - APPWRITE_DATABASE_ID");
    process.exit(1);
  }
  
  console.log("✅ Configuration Appwrite validée");
  console.log(`  - Endpoint: ${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}`);
  console.log(`  - Project: ${process.env.NEXT_PUBLIC_APPWRITE_PROJECT}`);
  console.log(`  - Database: ${process.env.APPWRITE_DATABASE_ID}`);
  
  // Vérifier si les collections existent déjà
  const canProceed = await checkCollectionsExist();
  
  if (canProceed) {
    await createVerificationCollections();
  } else {
    console.log("⏭️  Création annulée ou collections déjà existantes");
  }
}

// Exécuter le script
if (require.main === module) {
  main().catch(console.error);
}

export { createVerificationCollections, checkCollectionsExist };
