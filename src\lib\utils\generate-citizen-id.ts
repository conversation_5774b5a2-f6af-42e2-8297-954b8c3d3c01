import { createHash } from "crypto";

// TODO
export function generateCitizenId(
  birthDate: Date,
  registrationDate: Date,
  databaseId: string
): string {
  const birthDateStr = birthDate.toISOString().slice(0, 10).replace(/-/g, "");
  const registrationDateStr = registrationDate
    .toISOString()
    .slice(0, 10)
    .replace(/-/g, "");
  const hash = createHash("sha256");
  hash.update(`${birthDateStr}-${registrationDateStr}-${databaseId}`);
  const hashedId = hash.digest("hex").slice(0, 8);
  return `${birthDateStr}-${registrationDateStr}-${hashedId}`;
}
