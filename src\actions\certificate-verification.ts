"use server";

import { CertificateVerificationService } from "@/lib/database/certificate-verification";
import { CertificateCrypto } from "@/lib/utils/certificate-crypto";

export interface VerificationResult {
  isValid: boolean;
  status: "valid" | "expired" | "revoked" | "not_found" | "invalid_format";
  message: string;
  certificateInfo?: {
    reference: string;
    issuedAt: string;
    expiresAt: string;
    issuerName: string;
    region: string;
    commune: string;
    quartier: string;
    verificationCount: number;
    lastVerifiedAt?: string;
  };
  securityInfo?: {
    isIntegrityValid: boolean;
    isTimestampValid: boolean;
    errors: string[];
  };
}

/**
 * Vérifie l'authenticité d'un certificat par son hash
 */
export async function verifyCertificate(
  hash: string
): Promise<VerificationResult> {
  try {
    // Validation du format du hash
    if (!hash || hash.length !== 32 || !/^[a-f0-9]+$/i.test(hash)) {
      return {
        isValid: false,
        status: "invalid_format",
        message: "Format de hash invalide",
      };
    }

    // Vérification dans la base de données
    const validationResult =
      await CertificateVerificationService.validateCertificate(hash);

    if (!validationResult.verification) {
      return {
        isValid: false,
        status: validationResult.status,
        message: validationResult.message,
      };
    }

    const verification = validationResult.verification;

    // Préparation des informations du certificat (sans données sensibles)
    const certificateInfo = {
      reference: `${verification.metadata.region
        .substring(0, 3)
        .toUpperCase()}-***-${verification.issuedAt.substring(0, 4)}`,
      issuedAt: verification.issuedAt,
      expiresAt: verification.expiresAt,
      issuerName: verification.metadata.issuerName,
      region: verification.metadata.region,
      commune: verification.metadata.commune,
      quartier: verification.metadata.quartier,
      verificationCount: verification.verificationCount,
      lastVerifiedAt: verification.lastVerifiedAt,
    };

    return {
      isValid: validationResult.isValid,
      status: validationResult.status,
      message: validationResult.message,
      certificateInfo,
    };
  } catch (error) {
    console.error("Erreur lors de la vérification du certificat:", error);
    return {
      isValid: false,
      status: "not_found",
      message: "Erreur lors de la vérification. Veuillez réessayer.",
    };
  }
}

/**
 * Vérifie l'intégrité cryptographique d'un certificat
 */
export async function verifySecurityIntegrity(
  hash: string,
  salt: string,
  signature: string,
  timestamp: number,
  timestampHash: string
): Promise<{
  isValid: boolean;
  errors: string[];
}> {
  try {
    // Vérification de l'intégrité du hash
    const hashIntegrity = CertificateCrypto.verifyHashIntegrity(
      hash,
      salt,
      signature
    );

    // Vérification de l'horodatage
    const timestampIntegrity = CertificateCrypto.verifyTimestamp(
      timestamp,
      timestampHash
    );

    // Validation complète
    const securityValidation = CertificateCrypto.validateCertificateSecurity(
      hash,
      salt,
      signature,
      timestamp,
      timestampHash
    );

    return {
      isValid:
        hashIntegrity && timestampIntegrity && securityValidation.isValid,
      errors: securityValidation.errors,
    };
  } catch (error) {
    console.error("Erreur lors de la vérification de sécurité:", error);
    return {
      isValid: false,
      errors: ["Erreur lors de la vérification de sécurité"],
    };
  }
}

/**
 * Révoque un certificat (action administrative)
 */
export async function revokeCertificate(
  hash: string,
  reason: string,
  adminId: string
): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // TODO: Vérifier les permissions de l'administrateur
    // const hasPermission = await checkAdminPermissions(adminId, 'revoke_certificate');
    // if (!hasPermission) {
    //   return {
    //     success: false,
    //     message: 'Permissions insuffisantes pour révoquer un certificat'
    //   };
    // }

    const success = await CertificateVerificationService.revokeCertificate(
      hash,
      reason
    );

    if (success) {
      // TODO: Log de l'action administrative
      console.log(`Certificat ${hash} révoqué par ${adminId}: ${reason}`);

      return {
        success: true,
        message: "Certificat révoqué avec succès",
      };
    } else {
      return {
        success: false,
        message: "Impossible de révoquer le certificat",
      };
    }
  } catch (error) {
    console.error("Erreur lors de la révocation:", error);
    return {
      success: false,
      message: "Erreur lors de la révocation du certificat",
    };
  }
}

/**
 * Récupère les statistiques de vérification
 */
export async function getVerificationStats(certificateId?: string): Promise<{
  totalVerifications: number;
  validCertificates: number;
  expiredCertificates: number;
  revokedCertificates: number;
}> {
  try {
    return await CertificateVerificationService.getVerificationStats(
      certificateId
    );
  } catch (error) {
    console.error("Erreur lors de la récupération des statistiques:", error);
    return {
      totalVerifications: 0,
      validCertificates: 0,
      expiredCertificates: 0,
      revokedCertificates: 0,
    };
  }
}

/**
 * Recherche de certificats par critères (pour l'administration)
 */
export async function searchCertificates(criteria: {
  region?: string;
  commune?: string;
  quartier?: string;
  issuerName?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: "valid" | "expired" | "revoked";
}): Promise<{
  certificates: Array<{
    hash: string;
    reference: string;
    issuedAt: string;
    expiresAt: string;
    status: string;
    issuerName: string;
    location: string;
    verificationCount: number;
  }>;
  total: number;
}> {
  try {
    return await CertificateVerificationService.searchCertificates(criteria);
  } catch (error) {
    console.error("Erreur lors de la recherche:", error);
    return {
      certificates: [],
      total: 0,
    };
  }
}

/**
 * Génère un rapport de vérification pour audit
 */
export async function generateVerificationReport(
  hash: string,
  includeSecurityDetails: boolean = false
): Promise<{
  success: boolean;
  report?: {
    certificateHash: string;
    verificationTimestamp: string;
    status: string;
    verificationCount: number;
    securityChecks?: {
      hashIntegrity: boolean;
      timestampValidity: boolean;
      overallSecurity: boolean;
    };
    metadata: {
      issuer: string;
      location: string;
      issuedAt: string;
      expiresAt: string;
    };
  };
  error?: string;
}> {
  try {
    const verification =
      await CertificateVerificationService.getVerificationByHash(hash);

    if (!verification) {
      return {
        success: false,
        error: "Certificat non trouvé",
      };
    }

    const report = {
      certificateHash: hash,
      verificationTimestamp: new Date().toISOString(),
      status: verification.isRevoked
        ? "revoked"
        : new Date() > new Date(verification.expiresAt)
        ? "expired"
        : "valid",
      verificationCount: verification.verificationCount,
      metadata: {
        issuer: verification.metadata.issuerName,
        location: `${verification.metadata.quartier}, ${verification.metadata.commune}, ${verification.metadata.region}`,
        issuedAt: verification.issuedAt,
        expiresAt: verification.expiresAt,
      },
    };

    // Ajout des détails de sécurité si demandé
    if (includeSecurityDetails) {
      // TODO: Récupérer les données de sécurité depuis la base
      (report as any).securityChecks = {
        hashIntegrity: true,
        timestampValidity: true,
        overallSecurity: true,
      };
    }

    return {
      success: true,
      report,
    };
  } catch (error) {
    console.error("Erreur lors de la génération du rapport:", error);
    return {
      success: false,
      error: "Erreur lors de la génération du rapport",
    };
  }
}
