"use client";

import { UserAvatar } from "@/components/ui/user-avatar";
import type { User } from "@/types/auth";
import { motion } from "framer-motion";
import { ChefQuickActions } from "./quick-actions";
import { RecentCertificates } from "./recent-certificates";
import { ChefDashboardStats } from "./stats";

interface ChefDashboardProps {
  user: User;
}

export function ChefDashboard({ user }: ChefDashboardProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-neutral-50/50 to-accent-primary/5">
      {/* Fond décoratif */}
      <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(0,77,64,0.08),transparent_50%)]" />
        <div className="absolute inset-0 bg-[url('/patterns/topography.svg')] opacity-[0.02]" />
      </div>

      <div className="container mx-auto px-6 py-8 max-w-[1600px] space-y-6">
        {/* En-tête avec avatar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8"
        >
          {/* Cercles décoratifs */}
          <div className="absolute -top-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
          <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-secondary/5 rounded-full blur-2xl" />

          <div className="relative flex items-center gap-6">
            <UserAvatar
              name={user.name}
              src={user.prefs?.avatarUrl}
              size="lg"
              showStatus
              status="online"
              className="w-20 h-20 ring-4 ring-white shadow-xl"
            />
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 bg-clip-text text-transparent">
                Chef de quartier - {user.name || "Non assigné"}
              </h1>
              <p className="text-neutral-600 mt-1">
                Gérez les demandes de certificats et les agents de votre
                quartier
              </p>
            </div>
          </div>
        </motion.div>

        {/* Actions rapides */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8"
        >
          <div className="absolute -top-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
          <ChefQuickActions />
        </motion.div>

        {/* Statistiques */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8"
        >
          <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-secondary/5 rounded-full blur-2xl" />
          <ChefDashboardStats />
        </motion.div>

        {/* Certificats récents */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-neutral-200/60 p-8"
        >
          <div className="absolute -top-12 -right-12 w-32 h-32 bg-accent-primary/5 rounded-full blur-2xl" />
          <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-accent-secondary/5 rounded-full blur-2xl" />
          <RecentCertificates />
        </motion.div>
      </div>
    </div>
  );
}
