/**
 * Génère une référence unique pour un certificat
 * Format: NCR-YYYYMMDD-XXXXX
 * où XXXXX est un nombre aléatoire à 5 chiffres
 */
export function generateReference(prefix: string = "NCR"): string {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  // Génère un nombre aléatoire à 5 chiffres
  const random = Math.floor(Math.random() * 100000)
    .toString()
    .padStart(5, "0");

  return `${prefix}-${year}${month}${day}-${random}`;
}

/**
 * Valide le format d'une référence
 */
export function isValidReference(reference: string): boolean {
  const pattern = /^[A-Z]{2,3}-\d{8}-\d{5}$/;
  return pattern.test(reference);
}

/**
 * Extrait la date d'une référence
 */
export function getDateFromReference(reference: string): Date | null {
  if (!isValidReference(reference)) return null;

  const dateStr = reference.split("-")[1];
  const year = parseInt(dateStr.substring(0, 4));
  const month = parseInt(dateStr.substring(4, 6)) - 1;
  const day = parseInt(dateStr.substring(6, 8));

  return new Date(year, month, day);
}

// TODO
export function generateCertificateReference(
  region: string,
  commune: string
): string {
  const prefix = "NCR-";
  const regionCode = region.substring(0, 3).toUpperCase();
  const communeCode = commune.toUpperCase();
  const reference = generateReference(prefix);
  return `${prefix}-${regionCode}-${communeCode}-${reference}`;
}
