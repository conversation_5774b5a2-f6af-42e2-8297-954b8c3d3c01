import { getCurrentUser } from "@/actions/auth/session";
import { UserAvatar } from "@/components/ui/user-avatar";

export const dynamic = "force-dynamic";

export default async function ProfilePage() {
  const { user } = await getCurrentUser();

  if (!user) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <UserAvatar
          name={user.name}
          src={user.prefs?.avatarUrl}
          size="lg"
          showStatus
          status="online"
        />
        <div>
          <h2 className="text-2xl font-bold">{user.name}</h2>
          <p className="text-neutral-600">{user.email}</p>
        </div>
      </div>
      {/* ... reste du contenu ... */}
    </div>
  );
}
