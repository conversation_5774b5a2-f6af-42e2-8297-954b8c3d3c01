"use client";

import { getCertificateById } from "@/actions/citizen/certificates";
import { Certificate } from "@/actions/types";
import { CertificateActions } from "@/components/certificates/certificate-actions";
import { CertificateDetails } from "@/components/certificates/certificate-details";
import { CertificateDocuments } from "@/components/certificates/certificate-documents";
import { CertificateTimeline } from "@/components/certificates/certificate-timeline";
import { DashboardHeader } from "@/components/dashboard/header";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import { Loader } from "@/components/ui/loader";

export default function CertificateDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();

  const { data: certificateData, isLoading } = useQuery({
    queryKey: ["certificate", params.id],
    queryFn: async () => {
      const response = await getCertificateById(params.id as string);
      return response.certificate as Certificate;
    },
  });

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <Loader
        fullScreen
        size="lg"
        variant="primary"
        text="Chargement du certificat..."
      />
    );
  }

  if (!certificateData) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold">Certificat non trouvé</h1>
        <p className="text-neutral-500 mt-2">
          Le certificat que vous recherchez n&apos;existe pas ou a été supprimé.
        </p>
      </div>
    );
  }

  // Déterminer le rôle de l'utilisateur pour ce certificat
  const userRole = (() => {
    if (!user) return null;
    if (user.prefs?.role === "admin") return "admin";
    if (certificateData.chefId === user.$id) return "chef";
    if (certificateData.agentId === user.$id) return "agent";
    if (certificateData.citizenId === user.$id) return "citizen";
    return null;
  })();

  // Vérifier les permissions
  if (!userRole) {
    router.push("/dashboard");
    return null;
  }

  return (
    <div className="space-y-8 p-8">
      <DashboardHeader
        heading={`Certificat ${certificateData.reference}`}
        text="Détails et suivi de la demande de certificat"
      >
        <CertificateActions certificate={certificateData} userRole={userRole} />
      </DashboardHeader>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Informations principales */}
        <div className="md:col-span-2 space-y-6">
          <CertificateDetails
            certificate={certificateData}
            userRole={userRole}
          />
          <CertificateDocuments
            certificate={certificateData}
            userRole={userRole}
            user={{
              $id: user.$id,
              name: user.name,
            }}
          />
        </div>

        {/* Timeline et statut */}
        <div>
          <CertificateTimeline certificate={certificateData} />
        </div>
      </div>
    </div>
  );
}
