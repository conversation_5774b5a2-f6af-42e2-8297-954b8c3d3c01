import { describe, it, expect } from "vitest";
import {
  generateReference,
  isValidReference,
  getDateFromReference,
  generateCertificateReference,
  isValidCertificateReference,
  parseCertificateReference,
} from "../reference";

describe("generateReference", () => {
  it("should generate a valid reference with default prefix", () => {
    const ref = generateReference();
    expect(ref).toMatch(/^NCR-\d{8}-\d{5}$/);
  });

  it("should generate a valid reference with custom prefix", () => {
    const ref = generateReference("TEST");
    expect(ref).toMatch(/^TEST-\d{8}-\d{5}$/);
  });

  it("should clean prefix with trailing dashes", () => {
    const ref = generateReference("TEST-");
    expect(ref).toMatch(/^TEST-\d{8}-\d{5}$/);
    expect(ref).not.toContain("TEST--");
  });

  it("should clean prefix with multiple trailing dashes", () => {
    const ref = generateReference("TEST---");
    expect(ref).toMatch(/^TEST-\d{8}-\d{5}$/);
    expect(ref).not.toContain("TEST----");
  });
});

describe("isValidReference", () => {
  it("should validate correct reference formats", () => {
    expect(isValidReference("NCR-20241201-12345")).toBe(true);
    expect(isValidReference("TEST-20241201-67890")).toBe(true);
    expect(isValidReference("ABC123-20241201-11111")).toBe(true);
  });

  it("should reject invalid reference formats", () => {
    expect(isValidReference("")).toBe(false);
    expect(isValidReference("NCR-2024120-12345")).toBe(false); // Wrong date length
    expect(isValidReference("NCR-20241201-1234")).toBe(false); // Wrong number length
    expect(isValidReference("ncr-20241201-12345")).toBe(false); // Lowercase prefix
  });
});

describe("generateCertificateReference", () => {
  it("should generate a valid certificate reference", () => {
    const ref = generateCertificateReference("Conakry", "Matam");
    expect(ref).toMatch(/^NCR-[A-Z0-9]{1,3}-[A-Z0-9]{1,6}-\d{8}-\d{5}$/);
    expect(ref).toContain("CON"); // First 3 chars of region
    expect(ref).toContain("MATAM"); // Commune name
  });

  it("should handle special characters in region and commune names", () => {
    const ref = generateCertificateReference("Boké-Fria", "N'Zérékoré");
    expect(ref).toMatch(/^NCR-[A-Z0-9]{1,3}-[A-Z0-9]{1,6}-\d{8}-\d{5}$/);
    expect(ref).toContain("BOK"); // Special chars removed
    expect(ref).toContain("NZEREK"); // Special chars removed, limited to 6 chars
  });

  it("should not contain double dashes", () => {
    const ref = generateCertificateReference("Test", "Commune");
    expect(ref).not.toContain("--");
    expect(ref.split("-")).toHaveLength(5); // NCR-REG-COMMUNE-DATE-NUMBER
  });

  it("should limit commune code length", () => {
    const ref = generateCertificateReference("Region", "VeryLongCommuneName");
    const parts = ref.split("-");
    expect(parts[2]).toHaveLength(6); // Limited to 6 characters
    expect(parts[2]).toBe("VERYLO");
  });
});

describe("isValidCertificateReference", () => {
  it("should validate correct certificate reference formats", () => {
    expect(isValidCertificateReference("NCR-CON-MATAM-20241201-12345")).toBe(true);
    expect(isValidCertificateReference("NCR-A-B-20241201-67890")).toBe(true);
    expect(isValidCertificateReference("NCR-ABC-DEFGHI-20241201-11111")).toBe(true);
  });

  it("should reject invalid certificate reference formats", () => {
    expect(isValidCertificateReference("")).toBe(false);
    expect(isValidCertificateReference("NCR-CON-MATAM-2024120-12345")).toBe(false); // Wrong date
    expect(isValidCertificateReference("NCR-TOOLONG-MATAM-20241201-12345")).toBe(false); // Region too long
    expect(isValidCertificateReference("NCR-CON-TOOLONGCOMMUNE-20241201-12345")).toBe(false); // Commune too long
    expect(isValidCertificateReference("WRONG-CON-MATAM-20241201-12345")).toBe(false); // Wrong prefix
  });
});

describe("parseCertificateReference", () => {
  it("should parse valid certificate reference correctly", () => {
    const ref = "NCR-CON-MATAM-20241201-12345";
    const result = parseCertificateReference(ref);
    
    expect(result).not.toBeNull();
    expect(result!.regionCode).toBe("CON");
    expect(result!.communeCode).toBe("MATAM");
    expect(result!.date).toEqual(new Date(2024, 11, 1)); // Month is 0-indexed
    expect(result!.randomNumber).toBe("12345");
  });

  it("should return null for invalid certificate reference", () => {
    expect(parseCertificateReference("invalid-ref")).toBeNull();
    expect(parseCertificateReference("")).toBeNull();
    expect(parseCertificateReference("NCR-CON-MATAM-invalid-12345")).toBeNull();
  });
});

describe("Integration tests", () => {
  it("should create and validate certificate reference correctly", () => {
    const ref = generateCertificateReference("Conakry", "Matam");
    
    // Should be valid
    expect(isValidCertificateReference(ref)).toBe(true);
    
    // Should parse correctly
    const parsed = parseCertificateReference(ref);
    expect(parsed).not.toBeNull();
    expect(parsed!.regionCode).toBe("CON");
    expect(parsed!.communeCode).toBe("MATAM");
  });

  it("should not have double dashes in any generated reference", () => {
    const simpleRef = generateReference("TEST-");
    const certRef = generateCertificateReference("Test-Region", "Test-Commune");
    
    expect(simpleRef).not.toContain("--");
    expect(certRef).not.toContain("--");
  });
});
