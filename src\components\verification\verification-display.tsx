"use client";

import { useState } from "react";
import { CheckCircle, XCircle, AlertTriangle, Clock, Shield, Eye, EyeOff } from "lucide-react";
import { VerificationResult } from "@/actions/certificate-verification";

interface VerificationDisplayProps {
  result: VerificationResult;
  hash: string;
  debugInfo?: {
    hash: string;
    certificateId?: string;
    timestamp?: string;
    verificationTime: string;
  };
}

export function VerificationDisplay({ result, hash, debugInfo }: VerificationDisplayProps) {
  const [showDebug, setShowDebug] = useState(false);

  const getStatusIcon = () => {
    switch (result.status) {
      case 'valid':
        return <CheckCircle className="w-16 h-16 text-green-500" />;
      case 'expired':
        return <Clock className="w-16 h-16 text-orange-500" />;
      case 'revoked':
        return <XCircle className="w-16 h-16 text-red-500" />;
      default:
        return <AlertTriangle className="w-16 h-16 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (result.status) {
      case 'valid':
        return 'border-green-500 bg-green-50';
      case 'expired':
        return 'border-orange-500 bg-orange-50';
      case 'revoked':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-500 bg-gray-50';
    }
  };

  const getStatusText = () => {
    switch (result.status) {
      case 'valid':
        return 'CERTIFICAT VALIDE';
      case 'expired':
        return 'CERTIFICAT EXPIRÉ';
      case 'revoked':
        return 'CERTIFICAT RÉVOQUÉ';
      case 'not_found':
        return 'CERTIFICAT NON TROUVÉ';
      default:
        return 'STATUT INCONNU';
    }
  };

  return (
    <div className="space-y-6">
      {/* Résultat principal */}
      <div className={`border-4 rounded-lg p-8 text-center ${getStatusColor()}`}>
        <div className="flex justify-center mb-4">
          {getStatusIcon()}
        </div>
        
        <h3 className="text-2xl font-bold mb-2">
          {getStatusText()}
        </h3>
        
        <p className="text-lg text-gray-700 mb-4">
          {result.message}
        </p>

        {/* Hash du certificat */}
        <div className="bg-white rounded-lg p-4 mb-4">
          <p className="text-sm text-gray-600 mb-1">Hash de vérification:</p>
          <p className="font-mono text-sm break-all">
            {hash}
          </p>
        </div>

        {/* Indicateur de sécurité */}
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
          <Shield className="w-4 h-4" />
          <span>Vérification sécurisée</span>
        </div>
      </div>

      {/* Informations du certificat (si valide) */}
      {result.certificateInfo && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h4 className="text-lg font-semibold mb-4 text-gray-800">
            Informations du Certificat
          </h4>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Référence:</p>
              <p className="font-semibold">{result.certificateInfo.reference}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Émis le:</p>
              <p className="font-semibold">
                {new Date(result.certificateInfo.issuedAt).toLocaleDateString('fr-FR')}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Expire le:</p>
              <p className="font-semibold">
                {new Date(result.certificateInfo.expiresAt).toLocaleDateString('fr-FR')}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Autorité émettrice:</p>
              <p className="font-semibold">{result.certificateInfo.issuerName}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Localisation:</p>
              <p className="font-semibold">
                {result.certificateInfo.quartier}, {result.certificateInfo.commune}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Région:</p>
              <p className="font-semibold">{result.certificateInfo.region}</p>
            </div>
          </div>

          {/* Statistiques de vérification */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Nombre de vérifications:</span>
              <span className="font-semibold">{result.certificateInfo.verificationCount}</span>
            </div>
            {result.certificateInfo.lastVerifiedAt && (
              <div className="flex items-center justify-between text-sm text-gray-600 mt-1">
                <span>Dernière vérification:</span>
                <span className="font-semibold">
                  {new Date(result.certificateInfo.lastVerifiedAt).toLocaleDateString('fr-FR')}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Informations de sécurité */}
      {result.securityInfo && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h4 className="text-lg font-semibold mb-4 text-gray-800">
            Vérifications de Sécurité
          </h4>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Intégrité du hash:</span>
              <span className={`font-semibold ${result.securityInfo.isIntegrityValid ? 'text-green-600' : 'text-red-600'}`}>
                {result.securityInfo.isIntegrityValid ? '✓ Valide' : '✗ Invalide'}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Horodatage:</span>
              <span className={`font-semibold ${result.securityInfo.isTimestampValid ? 'text-green-600' : 'text-red-600'}`}>
                {result.securityInfo.isTimestampValid ? '✓ Valide' : '✗ Invalide'}
              </span>
            </div>
          </div>

          {result.securityInfo.errors.length > 0 && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
              <p className="text-sm font-semibold text-red-800 mb-1">Erreurs détectées:</p>
              <ul className="text-sm text-red-700 list-disc list-inside">
                {result.securityInfo.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Informations de debug (si disponibles) */}
      {debugInfo && (
        <div className="bg-gray-100 rounded-lg p-4">
          <button
            onClick={() => setShowDebug(!showDebug)}
            className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
          >
            {showDebug ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span>{showDebug ? 'Masquer' : 'Afficher'} les informations de debug</span>
          </button>
          
          {showDebug && (
            <div className="mt-3 space-y-2 text-sm font-mono">
              <div>
                <span className="text-gray-600">Hash:</span>
                <span className="ml-2 break-all">{debugInfo.hash}</span>
              </div>
              {debugInfo.certificateId && (
                <div>
                  <span className="text-gray-600">Certificate ID:</span>
                  <span className="ml-2">{debugInfo.certificateId}</span>
                </div>
              )}
              {debugInfo.timestamp && (
                <div>
                  <span className="text-gray-600">Timestamp:</span>
                  <span className="ml-2">{debugInfo.timestamp}</span>
                </div>
              )}
              <div>
                <span className="text-gray-600">Verification Time:</span>
                <span className="ml-2">{debugInfo.verificationTime}</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h5 className="font-semibold text-blue-800 mb-2">
          Comment vérifier un certificat ?
        </h5>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Scannez le QR code présent sur le certificat papier</li>
          <li>• Ou saisissez manuellement le hash de vérification</li>
          <li>• Le système vérifie automatiquement l'authenticité</li>
          <li>• Seuls les certificats officiels apparaîtront comme valides</li>
        </ul>
      </div>
    </div>
  );
}
