"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils/cn";
import { motion } from "framer-motion";
import {
  LayoutDashboard,
  FileText,
  Bell,
  Settings,
  HelpCircle,
  Users,
  LucideIcon,
  Baby,
} from "lucide-react";
import Image from "next/image";
import { useAuth } from "@/hooks/use-auth";
import { ROLES } from "@/actions/auth/constants";

const adminNavItems = [
  {
    title: "Tableau de bord",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Utilisateurs",
    href: "/admin/users",
    icon: Users,
  },
  {
    title: "Certificats",
    href: "/dashboard/certificates",
    icon: FileText,
  },
  {
    title: "Déclaration de naissance",
    href: "/dashboard/declaration-naissance",
    icon: Baby,
  },
  {
    title: "Notifications",
    href: "/dashboard/notifications",
    icon: Bell,
  },
  {
    title: "Paramè<PERSON>",
    href: "/dashboard/parameters",
    icon: Settings,
  },
  {
    title: "Aide",
    href: "/dashboard/help",
    icon: HelpCircle,
  },
];

const userNavItems = [
  {
    title: "Tableau de bord",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Certificats",
    href: "/dashboard/certificates",
    icon: FileText,
  },
  {
    title: "Déclaration de naissance",
    href: "/dashboard/declaration-naissance",
    icon: Baby,
  },
  {
    title: "Notifications",
    href: "/dashboard/notifications",
    icon: Bell,
  },
  {
    title: "Paramètres",
    href: "/dashboard/parameters",
    icon: Settings,
  },
  {
    title: "Aide",
    href: "/dashboard/help",
    icon: HelpCircle,
  },
];

interface NavItem {
  title: string;
  href: string;
  icon: LucideIcon;
}

interface DashboardNavProps extends React.HTMLAttributes<HTMLDivElement> {}

export function DashboardNav({ className, ...props }: DashboardNavProps) {
  const pathname = usePathname();
  const { user } = useAuth();

  const navItems: NavItem[] =
    user?.prefs?.role === ROLES.ADMIN ? adminNavItems : userNavItems;

  return (
    <div
      className={cn(
        "flex h-screen w-72 flex-col border-r bg-card px-4 py-6",
        className
      )}
      {...props}
    >
      {/* Logo */}
      <div className="mb-8 px-4">
        <Link
          href="/"
          className="flex items-center space-x-2 transition-opacity hover:opacity-80"
        >
          <div className="relative w-12 h-12 transform transition-transform group-hover:scale-105">
            <div className="absolute inset-0 bg-gradient-to-br from-[#004D40] to-[#00796B] rounded-xl blur opacity-20" />
            <div className="relative">
              <Image
                src="/logo.png"
                alt="Logo NCR"
                width={48}
                height={48}
                className="object-contain"
                priority
              />
            </div>
          </div>
          <div>
            <span className="text-2xl font-bold bg-gradient-to-r from-[#004D40] via-yellow-600 to-red-600 bg-clip-text text-transparent">
              NCR
            </span>
          </div>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-2">
        {navItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "group relative flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-accent"
              )}
            >
              {isActive && (
                <motion.div
                  layoutId="activeNav"
                  className="absolute inset-0 rounded-lg bg-primary"
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 500,
                    damping: 30,
                  }}
                />
              )}
              <Icon
                className={cn(
                  "mr-2 h-4 w-4",
                  isActive ? "text-primary-foreground" : "text-muted-foreground"
                )}
              />
              <span className="relative">{item.title}</span>
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="mt-auto border-t pt-4">
        <div className="px-4 py-2">
          <p className="text-xs text-muted-foreground">
            © 2024 NCR Ouestech. Tous droits réservés.
          </p>
        </div>
      </div>
    </div>
  );
}
