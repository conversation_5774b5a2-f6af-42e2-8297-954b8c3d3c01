import { createAdminClient } from "@/lib/server/appwrite";
import { DATABASE_ID } from "@/lib/server/database";
import { ID, Query } from "node-appwrite";

// Collection ID pour les vérifications de certificats
export const CERTIFICATE_VERIFICATIONS_COLLECTION_ID = "certificate_verifications";

export interface CertificateVerification {
  $id: string;
  hash: string;
  certificateId: string;
  citizenId: string;
  issuedAt: string;
  expiresAt: string;
  isValid: boolean;
  isRevoked: boolean;
  verificationCount: number;
  lastVerifiedAt?: string;
  metadata: {
    issuerType: string; // 'chef' | 'agent' | 'admin'
    issuerId: string;
    issuerName: string;
    region: string;
    commune: string;
    quartier: string;
  };
  createdAt: string;
  updatedAt: string;
}

export class CertificateVerificationService {
  private static async getDatabase() {
    const { databases } = await createAdminClient();
    return databases;
  }

  /**
   * Crée un enregistrement de vérification pour un certificat
   */
  static async createVerification(data: {
    hash: string;
    certificateId: string;
    citizenId: string;
    issuedAt: Date;
    expiresAt: Date;
    metadata: CertificateVerification['metadata'];
  }): Promise<CertificateVerification> {
    const databases = await this.getDatabase();
    
    const verification: Omit<CertificateVerification, '$id'> = {
      hash: data.hash,
      certificateId: data.certificateId,
      citizenId: data.citizenId,
      issuedAt: data.issuedAt.toISOString(),
      expiresAt: data.expiresAt.toISOString(),
      isValid: true,
      isRevoked: false,
      verificationCount: 0,
      metadata: data.metadata,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return await databases.createDocument(
      DATABASE_ID,
      CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
      ID.unique(),
      verification
    ) as CertificateVerification;
  }

  /**
   * Récupère une vérification par hash
   */
  static async getVerificationByHash(hash: string): Promise<CertificateVerification | null> {
    try {
      const databases = await this.getDatabase();
      
      const { documents } = await databases.listDocuments(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        [Query.equal("hash", hash), Query.limit(1)]
      );

      return documents.length > 0 ? documents[0] as CertificateVerification : null;
    } catch (error) {
      console.error("Erreur lors de la récupération de la vérification:", error);
      return null;
    }
  }

  /**
   * Met à jour le compteur de vérifications
   */
  static async incrementVerificationCount(hash: string): Promise<void> {
    try {
      const verification = await this.getVerificationByHash(hash);
      if (!verification) return;

      const databases = await this.getDatabase();
      
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        verification.$id,
        {
          verificationCount: verification.verificationCount + 1,
          lastVerifiedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
    } catch (error) {
      console.error("Erreur lors de la mise à jour du compteur:", error);
    }
  }

  /**
   * Révoque un certificat
   */
  static async revokeCertificate(hash: string, reason?: string): Promise<boolean> {
    try {
      const verification = await this.getVerificationByHash(hash);
      if (!verification) return false;

      const databases = await this.getDatabase();
      
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        verification.$id,
        {
          isRevoked: true,
          isValid: false,
          metadata: {
            ...verification.metadata,
            revocationReason: reason || "Révoqué par l'administration",
            revokedAt: new Date().toISOString(),
          },
          updatedAt: new Date().toISOString(),
        }
      );

      return true;
    } catch (error) {
      console.error("Erreur lors de la révocation:", error);
      return false;
    }
  }

  /**
   * Vérifie si un certificat est valide
   */
  static async validateCertificate(hash: string): Promise<{
    isValid: boolean;
    status: 'valid' | 'expired' | 'revoked' | 'not_found';
    verification?: CertificateVerification;
    message: string;
  }> {
    const verification = await this.getVerificationByHash(hash);
    
    if (!verification) {
      return {
        isValid: false,
        status: 'not_found',
        message: 'Certificat non trouvé dans la base de données'
      };
    }

    // Incrémenter le compteur de vérifications
    await this.incrementVerificationCount(hash);

    if (verification.isRevoked) {
      return {
        isValid: false,
        status: 'revoked',
        verification,
        message: 'Ce certificat a été révoqué'
      };
    }

    const now = new Date();
    const expiresAt = new Date(verification.expiresAt);

    if (now > expiresAt) {
      return {
        isValid: false,
        status: 'expired',
        verification,
        message: 'Ce certificat a expiré'
      };
    }

    return {
      isValid: true,
      status: 'valid',
      verification,
      message: 'Certificat valide'
    };
  }

  /**
   * Récupère les statistiques de vérification
   */
  static async getVerificationStats(certificateId?: string): Promise<{
    totalVerifications: number;
    validCertificates: number;
    expiredCertificates: number;
    revokedCertificates: number;
  }> {
    try {
      const databases = await this.getDatabase();
      
      const queries = certificateId 
        ? [Query.equal("certificateId", certificateId)]
        : [];

      const { documents } = await databases.listDocuments(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        queries
      );

      const now = new Date();
      
      const stats = documents.reduce((acc, doc) => {
        const verification = doc as CertificateVerification;
        const expiresAt = new Date(verification.expiresAt);
        
        acc.totalVerifications += verification.verificationCount;
        
        if (verification.isRevoked) {
          acc.revokedCertificates++;
        } else if (now > expiresAt) {
          acc.expiredCertificates++;
        } else {
          acc.validCertificates++;
        }
        
        return acc;
      }, {
        totalVerifications: 0,
        validCertificates: 0,
        expiredCertificates: 0,
        revokedCertificates: 0,
      });

      return stats;
    } catch (error) {
      console.error("Erreur lors de la récupération des statistiques:", error);
      return {
        totalVerifications: 0,
        validCertificates: 0,
        expiredCertificates: 0,
        revokedCertificates: 0,
      };
    }
  }
}
