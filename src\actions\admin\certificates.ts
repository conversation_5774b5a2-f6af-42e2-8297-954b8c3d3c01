"use server";

import { CERTIFICATE_STATUS, CERTIFICATE_TYPE } from "@/actions/auth/constants";
import { Certificate } from "@/actions/types";
import type { CertificateFilters } from "@/hooks/use-certificates";
import { createAdminClient } from "@/lib/server/appwrite";
import { CERTIFICATES_COLLECTION_ID, DATABASE_ID } from "@/lib/server/database";
import { Query } from "node-appwrite";

export type CertificatesResponse = {
  certificates: Certificate[];
  pagination: {
    total: number;
    page: number;
    limit: number;
  };
};

export async function getCertificates(
  filters: CertificateFilters = {}
): Promise<CertificatesResponse> {
  try {
    const { databases } = await createAdminClient();
    const queries: string[] = [];

    // Filtres
    if (filters.search) {
      queries.push(Query.search("reference", filters.search));
    }

    if (filters.status) {
      queries.push(Query.equal("status", filters.status));
    }

    // Pagination
    if (filters.limit) {
      queries.push(Query.limit(filters.limit));
    }

    if (filters.page) {
      const offset = filters.page * (filters.limit || 10);
      queries.push(Query.offset(offset));
    }

    // Récupération des certificats
    const { documents, total } = await databases.listDocuments(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      queries
    );

    return {
      certificates: documents.map((doc) => ({
        $id: doc.$id,
        reference: doc.reference,
        userId: doc.userId,
        citizenId: doc.citizenId,
        chefId: doc.chefId,
        userName: doc.userName,
        quartier: doc.quartier,
        status: doc.status,
        motif: doc.motif,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
        agentId: doc.agentId || null,
        validite: doc.validite || null,
        verifiedAt: doc.verifiedAt || null,
        signedAt: doc.signedAt || null,
        printedAt: doc.printedAt || null,
        rejectedAt: doc.rejectedAt || null,
        rejectionReason: doc.rejectionReason || null,
        readAt: doc.readAt || null,
        readBy: doc.readBy || null,
        verificationCode: doc.verificationCode || null,
        signature: doc.signature || null,
        qrCode: doc.qrCode || null,
        documentUrl: doc.documentUrl || null,
        metadata: doc.metadata || {},
        type: doc.type || CERTIFICATE_TYPE.RESIDENCE,
        version: doc.version || "1.0",
        expiresAt: doc.expiresAt || null,
        issuedAt: doc.issuedAt || null,
        deliveredAt: doc.deliveredAt || null,
        downloadedAt: doc.downloadedAt || null,
        rejectedBy: doc.rejectedBy || null,
        verifiedBy: doc.verifiedBy || null,
        verificationNotes: doc.verificationNotes || null,
        $collectionId: doc.$collectionId,
        $databaseId: doc.$databaseId,
        $createdAt: doc.$createdAt,
        $updatedAt: doc.$updatedAt,
        $permissions: doc.$permissions,
      })),
      pagination: {
        total,
        page: filters.page || 0,
        limit: filters.limit || 10,
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des certificats:", error);
    throw new Error("Impossible de récupérer les certificats");
  }
}

export async function updateCertificateStatus(
  certificateId: string,
  status: CERTIFICATE_STATUS
) {
  try {
    const { databases } = await createAdminClient();

    await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        status,
        updatedAt: new Date().toISOString(),
      }
    );

    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du statut:", error);
    throw error;
  }
}
