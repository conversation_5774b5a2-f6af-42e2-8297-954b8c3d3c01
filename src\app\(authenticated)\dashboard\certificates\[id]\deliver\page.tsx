"use client";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { getCertificateWithFullData } from "@/actions/certificates";
import { CertificateDetails } from "@/components/certificates/certificate-details";
import { CertificateTimeline } from "@/components/certificates/certificate-timeline";
import { DashboardHeader } from "@/components/dashboard/header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useCertificateActions } from "@/hooks/use-certificate-actions";
import { useCertificatePreview } from "@/hooks/use-certificate-preview";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, Loader2, Send } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState, useCallback } from "react";

export default function DeliverCertificatePage() {
  const params = useParams();
  const certificateId = params?.id as string;
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const { deliver, isLoading: isDelivering } = useCertificateActions();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);

  const {
    generatePreview,
    cleanupPreview,
    error: previewError,
  } = useCertificatePreview();

  // Récupération des données du certificat avec polling pendant la délivrance
  const { data: certificateData, isLoading: isLoadingCertificate } = useQuery({
    queryKey: ["certificate", certificateId],
    queryFn: () => getCertificateWithFullData(certificateId),
    enabled: !!certificateId && !!user,
    refetchInterval: isDelivering ? 2000 : false, // Poll toutes les 2 secondes pendant la délivrance
    staleTime: 0, // Désactiver le cache pendant la délivrance
  });

  const certificate = certificateData?.certificate;
  const isDelivered = certificate?.status === CERTIFICATE_STATUS.DELIVERED;

  // Fonction pour charger la prévisualisation
  const loadPreview = useCallback(async () => {
    if (!certificateId || isLoadingPreview) return;

    setIsLoadingPreview(true);
    try {
      // Nettoyer l'ancienne prévisualisation si elle existe
      if (previewUrl) {
        await cleanupPreview(previewUrl);
        setPreviewUrl(null);
      }

      const url = await generatePreview(certificateId);
      if (url) {
        setPreviewUrl(url);
      }
    } catch (err) {
      console.error(
        "Erreur lors de la génération de la prévisualisation:",
        err
      );
      toast({
        title: "Erreur de prévisualisation",
        description: "Impossible de générer la prévisualisation du certificat.",
        variant: "error",
      });
    } finally {
      setIsLoadingPreview(false);
    }
  }, [
    certificateId,
    isLoadingPreview,
    previewUrl,
    cleanupPreview,
    generatePreview,
    toast,
  ]);

  // Charger la prévisualisation quand le certificat change
  useEffect(() => {
    if (certificate) {
      const canDeliver =
        user?.prefs?.role === "admin" ||
        (user?.prefs?.role === "chef" &&
          certificate.status === CERTIFICATE_STATUS.SIGNED &&
          certificate.chefId === user.$id);

      if (!canDeliver && !isDelivered) {
        toast({
          title: "Accès non autorisé",
          description:
            "Vous n'avez pas les permissions nécessaires pour délivrer ce certificat.",
          variant: "error",
        });
        router.push("/dashboard/certificates");
        return;
      }

      if (
        certificate &&
        (certificate.status === CERTIFICATE_STATUS.SIGNED || isDelivered)
      ) {
        loadPreview();
      }
    }
  }, [
    certificate?.status,
    certificateId,
    certificate,
    isDelivered,
    loadPreview,
    router,
    toast,
    user?.$id,
    user?.prefs?.role,
  ]);

  // Nettoyer la prévisualisation au démontage
  useEffect(() => {
    return () => {
      if (previewUrl) {
        console.log("Preview URL:", previewUrl);
        cleanupPreview(previewUrl);
      }
    };
  }, [previewUrl, cleanupPreview]);

  const handleDeliver = async () => {
    if (!certificate || isDelivered || isDelivering) return;

    const deliveryTimeout = setTimeout(() => {
      toast({
        title: "Délai dépassé",
        description:
          "La délivrance prend plus de temps que prévu. Veuillez rafraîchir la page.",
        variant: "error",
      });
    }, 30000); // 30 secondes de timeout

    try {
      toast({
        title: "Délivrance en cours",
        description:
          "Veuillez patienter pendant la délivrance du certificat...",
      });

      await deliver(certificateId);

      clearTimeout(deliveryTimeout);

      toast({
        title: "Certificat délivré",
        description: "Le certificat a été délivré avec succès.",
        variant: "success",
      });

      // Recharger la prévisualisation
      await loadPreview();
    } catch (error) {
      clearTimeout(deliveryTimeout);

      toast({
        title: "Erreur lors de la délivrance",
        description:
          error instanceof Error
            ? error.message
            : "Une erreur est survenue lors de la délivrance.",
        variant: "error",
      });
    }
  };

  if (isLoadingCertificate || !certificate) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-accent-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-8 p-8">
      <DashboardHeader
        heading={`Délivrance du certificat ${certificate.reference}`}
        text={
          isDelivered
            ? "Certificat délivré"
            : "Vérification finale et délivrance du certificat"
        }
      >
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() =>
              router.push(`/dashboard/certificates/${certificateId}`)
            }
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour aux détails
          </Button>
          {!isDelivered && (
            <Button
              variant="default"
              onClick={handleDeliver}
              disabled={isDelivering || isDelivered}
              className="bg-green-600 hover:bg-green-700"
            >
              {isDelivering ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin text-accent-primary" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Délivrer le certificat
            </Button>
          )}
        </div>
      </DashboardHeader>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Informations principales */}
        <div className="md:col-span-2 space-y-6">
          <CertificateDetails
            certificate={certificate}
            userRole={user?.prefs?.role as "admin" | "chef"}
          />

          {/* Prévisualisation du PDF */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>
                {isDelivered
                  ? "Certificat délivré"
                  : "Prévisualisation du certificat"}
              </CardTitle>
              {isLoadingPreview && (
                <div className="flex items-center gap-2 text-sm text-neutral-500">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Chargement de la prévisualisation...
                </div>
              )}
            </CardHeader>
            <CardContent className="min-h-[700px] bg-neutral-50 rounded-lg p-0">
              {previewUrl ? (
                <iframe
                  src={previewUrl}
                  className="w-full h-[700px]"
                  style={{
                    border: "none",
                    backgroundColor: "white",
                  }}
                />
              ) : (
                <div className="flex items-center justify-center h-[700px]">
                  {isLoadingPreview ? (
                    <Loader2 className="h-8 w-8 animate-spin" />
                  ) : (
                    <p className="text-neutral-500">
                      {previewError || "Aucune prévisualisation disponible"}
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Timeline et vérifications */}
        <div className="space-y-6">
          <CertificateTimeline certificate={certificate} />
          <Card>
            <CardHeader>
              <CardTitle>Liste de vérification</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-green-600">
                <div className="h-2 w-2 rounded-full bg-green-600" />
                <span>Informations du citoyen vérifiées</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-green-600">
                <div className="h-2 w-2 rounded-full bg-green-600" />
                <span>Documents justificatifs validés</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-green-600">
                <div className="h-2 w-2 rounded-full bg-green-600" />
                <span>Signature du chef de quartier confirmée</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-green-600">
                <div className="h-2 w-2 rounded-full bg-green-600" />
                <span>Prêt pour la délivrance</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
