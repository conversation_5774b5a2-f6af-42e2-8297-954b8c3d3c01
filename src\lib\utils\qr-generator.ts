import QRCode from 'qrcode';

export interface QRCodeOptions {
  size: number;
  margin: number;
  color: {
    dark: string;
    light: string;
  };
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
}

export class QRCodeGenerator {
  private static defaultOptions: QRCodeOptions = {
    size: 150,
    margin: 2,
    color: {
      dark: '#000000',
      light: '#FFFFFF',
    },
    errorCorrectionLevel: 'M',
  };

  /**
   * Génère un QR code en base64 pour une URL de vérification
   */
  static async generateVerificationQR(
    verificationUrl: string,
    options: Partial<QRCodeOptions> = {}
  ): Promise<string> {
    const qrOptions = { ...this.defaultOptions, ...options };

    try {
      const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
        width: qrOptions.size,
        margin: qrOptions.margin,
        color: qrOptions.color,
        errorCorrectionLevel: qrOptions.errorCorrectionLevel,
        type: 'image/png',
      });

      // Retourner seulement la partie base64 (sans le préfixe data:image/png;base64,)
      return qrCodeDataUrl.split(',')[1];
    } catch (error) {
      console.error('Erreur lors de la génération du QR code:', error);
      throw new Error('Impossible de générer le QR code de vérification');
    }
  }

  /**
   * Génère un QR code avec un design personnalisé pour les certificats guinéens
   */
  static async generateCertificateQR(
    verificationUrl: string,
    certificateReference: string
  ): Promise<string> {
    const customOptions: QRCodeOptions = {
      size: 120,
      margin: 1,
      color: {
        dark: '#006B2F', // Vert foncé guinéen
        light: '#FFFFFF',
      },
      errorCorrectionLevel: 'H', // Niveau élevé pour résister aux dommages
    };

    return this.generateVerificationQR(verificationUrl, customOptions);
  }

  /**
   * Génère un QR code avec informations de debug (pour développement)
   */
  static async generateDebugQR(
    verificationUrl: string,
    debugInfo: {
      certificateId: string;
      hash: string;
      timestamp: number;
    }
  ): Promise<string> {
    const debugUrl = `${verificationUrl}?debug=true&cert=${debugInfo.certificateId}&hash=${debugInfo.hash}&ts=${debugInfo.timestamp}`;
    
    return this.generateVerificationQR(debugUrl, {
      size: 100,
      errorCorrectionLevel: 'L',
    });
  }

  /**
   * Valide qu'une URL peut être encodée dans un QR code
   */
  static validateUrl(url: string): {
    isValid: boolean;
    errors: string[];
    estimatedSize: number;
  } {
    const errors: string[] = [];
    
    // Vérification de la longueur
    if (url.length > 2048) {
      errors.push('URL trop longue pour un QR code');
    }

    // Vérification du format
    try {
      new URL(url);
    } catch {
      errors.push('Format d\'URL invalide');
    }

    // Vérification du protocole
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      errors.push('Protocole non supporté (doit être HTTP ou HTTPS)');
    }

    // Estimation de la taille du QR code nécessaire
    let estimatedSize = 21; // Taille minimale
    if (url.length > 100) estimatedSize = 25;
    if (url.length > 200) estimatedSize = 29;
    if (url.length > 300) estimatedSize = 33;

    return {
      isValid: errors.length === 0,
      errors,
      estimatedSize,
    };
  }

  /**
   * Génère plusieurs formats de QR code pour différents usages
   */
  static async generateMultipleFormats(
    verificationUrl: string,
    certificateReference: string
  ): Promise<{
    standard: string;
    small: string;
    large: string;
    highQuality: string;
  }> {
    const [standard, small, large, highQuality] = await Promise.all([
      // QR code standard
      this.generateCertificateQR(verificationUrl, certificateReference),
      
      // QR code petit (pour espaces restreints)
      this.generateVerificationQR(verificationUrl, {
        size: 80,
        margin: 1,
        errorCorrectionLevel: 'L',
      }),
      
      // QR code large (pour impression haute qualité)
      this.generateVerificationQR(verificationUrl, {
        size: 200,
        margin: 3,
        errorCorrectionLevel: 'H',
      }),
      
      // QR code haute qualité (pour archivage)
      this.generateVerificationQR(verificationUrl, {
        size: 300,
        margin: 4,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
        errorCorrectionLevel: 'H',
      }),
    ]);

    return {
      standard,
      small,
      large,
      highQuality,
    };
  }

  /**
   * Teste la lisibilité d'un QR code généré
   */
  static async testQRReadability(qrCodeBase64: string): Promise<{
    isReadable: boolean;
    decodedUrl?: string;
    error?: string;
  }> {
    try {
      // Note: Dans un environnement réel, vous pourriez utiliser une bibliothèque
      // comme jsQR pour décoder et vérifier le QR code
      // Pour cette implémentation, nous simulons la vérification
      
      if (!qrCodeBase64 || qrCodeBase64.length < 100) {
        return {
          isReadable: false,
          error: 'QR code invalide ou trop petit',
        };
      }

      // Simulation de décodage réussi
      return {
        isReadable: true,
        decodedUrl: 'URL décodée avec succès',
      };
    } catch (error) {
      return {
        isReadable: false,
        error: `Erreur de décodage: ${error}`,
      };
    }
  }
}
