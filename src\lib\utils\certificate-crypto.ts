import { createHash, createHmac, randomBytes } from "crypto";

// Clé secrète pour la signature (à définir dans les variables d'environnement)
const SECRET_KEY = process.env.CERTIFICATE_SECRET_KEY || "default-secret-key-change-in-production";
const SALT_LENGTH = 32;

export interface CertificateHashData {
  certificateId: string;
  citizenId: string;
  citizenIdUnique: string;
  timestamp: number;
  reference: string;
  issuerInfo: {
    type: string;
    id: string;
    name: string;
  };
  locationInfo: {
    region: string;
    commune: string;
    quartier: string;
  };
}

export class CertificateCrypto {
  /**
   * Génère un salt aléatoire
   */
  private static generateSalt(): string {
    return randomBytes(SALT_LENGTH).toString('hex');
  }

  /**
   * Génère un hash SHA-256 sécurisé pour un certificat
   */
  static generateSecureHash(data: CertificateHashData): {
    hash: string;
    salt: string;
    signature: string;
  } {
    const salt = this.generateSalt();
    const timestamp = Date.now();
    
    // Création de la chaîne de données à hasher
    const dataString = [
      data.certificateId,
      data.citizenId,
      data.citizenIdUnique,
      data.timestamp,
      data.reference,
      data.issuerInfo.type,
      data.issuerInfo.id,
      data.issuerInfo.name,
      data.locationInfo.region,
      data.locationInfo.commune,
      data.locationInfo.quartier,
      salt,
      timestamp,
    ].join('|');

    // Génération du hash principal
    const hash = createHash('sha256')
      .update(dataString)
      .update(SECRET_KEY)
      .digest('hex');

    // Génération de la signature HMAC pour vérification d'intégrité
    const signature = createHmac('sha256', SECRET_KEY)
      .update(hash)
      .update(salt)
      .digest('hex');

    return {
      hash: hash.substring(0, 32), // Tronquer à 32 caractères pour l'URL
      salt,
      signature,
    };
  }

  /**
   * Vérifie l'intégrité d'un hash
   */
  static verifyHashIntegrity(hash: string, salt: string, signature: string): boolean {
    try {
      const expectedSignature = createHmac('sha256', SECRET_KEY)
        .update(hash)
        .update(salt)
        .digest('hex');

      return signature === expectedSignature;
    } catch (error) {
      console.error("Erreur lors de la vérification de l'intégrité:", error);
      return false;
    }
  }

  /**
   * Génère un hash de vérification pour l'affichage dans le PDF
   */
  static generateDisplayHash(certificateData: CertificateHashData): string {
    const quickHash = createHash('md5')
      .update(`${certificateData.certificateId}-${certificateData.citizenIdUnique}`)
      .digest('hex')
      .substring(0, 8)
      .toUpperCase();

    return quickHash;
  }

  /**
   * Génère l'URL de vérification complète
   */
  static generateVerificationUrl(hash: string): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    return `${baseUrl}/verify/${hash}`;
  }

  /**
   * Génère un watermark dynamique basé sur les données du certificat
   */
  static generateDynamicWatermark(data: CertificateHashData): string {
    const elements = [
      data.citizenIdUnique.substring(0, 8),
      data.reference.split('-')[0], // Préfixe de la référence
      data.locationInfo.commune.substring(0, 3).toUpperCase(),
      new Date(data.timestamp).getFullYear().toString(),
    ];

    return elements.join('-');
  }

  /**
   * Génère un horodatage cryptographique
   */
  static generateTimestamp(): {
    timestamp: number;
    timestampHash: string;
    readableDate: string;
  } {
    const timestamp = Date.now();
    const date = new Date(timestamp);
    
    const timestampHash = createHash('sha256')
      .update(timestamp.toString())
      .update(SECRET_KEY)
      .digest('hex')
      .substring(0, 16);

    return {
      timestamp,
      timestampHash,
      readableDate: date.toISOString(),
    };
  }

  /**
   * Vérifie un horodatage cryptographique
   */
  static verifyTimestamp(timestamp: number, timestampHash: string): boolean {
    try {
      const expectedHash = createHash('sha256')
        .update(timestamp.toString())
        .update(SECRET_KEY)
        .digest('hex')
        .substring(0, 16);

      return timestampHash === expectedHash;
    } catch (error) {
      console.error("Erreur lors de la vérification de l'horodatage:", error);
      return false;
    }
  }

  /**
   * Génère des données de sécurité complètes pour un certificat
   */
  static generateCertificateSecurity(data: CertificateHashData): {
    verificationHash: string;
    displayHash: string;
    watermark: string;
    timestamp: ReturnType<typeof CertificateCrypto.generateTimestamp>;
    verificationUrl: string;
    salt: string;
    signature: string;
  } {
    const { hash, salt, signature } = this.generateSecureHash(data);
    const displayHash = this.generateDisplayHash(data);
    const watermark = this.generateDynamicWatermark(data);
    const timestamp = this.generateTimestamp();
    const verificationUrl = this.generateVerificationUrl(hash);

    return {
      verificationHash: hash,
      displayHash,
      watermark,
      timestamp,
      verificationUrl,
      salt,
      signature,
    };
  }

  /**
   * Valide toutes les données de sécurité d'un certificat
   */
  static validateCertificateSecurity(
    hash: string,
    salt: string,
    signature: string,
    timestamp: number,
    timestampHash: string
  ): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Vérification de l'intégrité du hash
    if (!this.verifyHashIntegrity(hash, salt, signature)) {
      errors.push("Intégrité du hash compromise");
    }

    // Vérification de l'horodatage
    if (!this.verifyTimestamp(timestamp, timestampHash)) {
      errors.push("Horodatage invalide");
    }

    // Vérification de la fraîcheur (pas plus de 5 ans)
    const maxAge = 5 * 365 * 24 * 60 * 60 * 1000; // 5 ans en millisecondes
    if (Date.now() - timestamp > maxAge) {
      errors.push("Certificat trop ancien");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
