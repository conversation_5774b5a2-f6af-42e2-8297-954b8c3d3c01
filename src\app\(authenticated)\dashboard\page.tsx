import { ROLES } from "@/actions/auth/constants";
import { getCurrentUser } from "@/actions/auth/session";
import { AdminQuickActions } from "@/components/dashboard/admin/quick-actions";
import { AdminRecentActivities } from "@/components/dashboard/admin/recent-activities";
import { AdminDashboardStats } from "@/components/dashboard/admin/stats";
import { ChefDashboard } from "@/components/dashboard/chef/chef-dashboard";
import { CitizenDashboard } from "@/components/dashboard/citizen/citizen-dashboard";
import { DashboardHeader } from "@/components/dashboard/header";
import { NotAuthorized } from "@/components/shared/not-authorized";
import { UserAvatar } from "@/components/ui/user-avatar";

export const dynamic = "force-dynamic";

export default async function DashboardPage() {
  const { user } = await getCurrentUser();

  if (!user) {
    return <NotAuthorized />;
  }

  // Vérification explicite des rôles
  switch (user.prefs?.role) {
    case ROLES.ADMIN:
      return (
        <div className="container mx-auto px-6 py-8 max-w-[1600px] space-y-8">
          <div className="bg-white/50 backdrop-blur-sm rounded-xl border border-neutral-200/60 p-6 shadow-sm">
            <div className="flex items-center gap-4">
              <UserAvatar
                name={user.name}
                src={user.prefs?.avatarUrl}
                size="lg"
                showStatus
                status="online"
              />
              <div>
                <DashboardHeader
                  heading="Administration"
                  text="Gérez l'ensemble de la plateforme NCR"
                />
              </div>
            </div>
          </div>

          <div className="grid gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-neutral-200/60 p-6">
              <AdminQuickActions />
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-neutral-200/60 p-6">
              <AdminDashboardStats />
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-neutral-200/60 p-6">
              <AdminRecentActivities />
            </div>
          </div>
        </div>
      );

    case ROLES.CITIZEN:
      return <CitizenDashboard user={user} />;

    // Préparation pour les futurs rôles
    case ROLES.AGENT:
      // TODO: Implémenter le dashboard agent
      return <NotAuthorized />;

    case ROLES.CHEF:
      return <ChefDashboard user={user} />;

    default:
      return <NotAuthorized />;
  }
}
