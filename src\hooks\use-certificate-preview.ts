"use client";

import { getCertificatePreview } from "@/actions/certificates";
import { useState, useCallback } from "react";

export function useCertificatePreview() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generatePreview = useCallback(async (certificateId: string) => {
    setIsGenerating(true);
    setError(null);
    try {
      const response = await getCertificatePreview(certificateId);
      if (!response.success) {
        throw new Error(
          response.error ||
            "Erreur lors de la génération de la prévisualisation"
        );
      }
      return response.previewUrl;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erreur inconnue");
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const cleanupPreview = useCallback((url: string) => {
    if (url.startsWith("blob:")) {
      URL.revokeObjectURL(url);
    }
  }, []);

  return {
    generatePreview,
    cleanupPreview,
    isGenerating,
    error,
    clearError: () => setError(null),
  };
}
