import Image from "next/image";

export function VerificationHeader() {
  return (
    <header className="text-center mb-8">
      <div className="flex items-center justify-center mb-6">
        <div className="w-16 h-16 relative mr-4">
          <Image
            src="/images/armoirie.png"
            alt="Armoirie de la République de Guinée"
            fill
            className="object-contain"
          />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-800">
            RÉPUBLIQUE DE GUINÉE
          </h1>
          <p className="text-sm text-gray-600">
            Travail - Justice - Solidarité
          </p>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold text-green-700 mb-2">
          Vérification de Certificat de Résidence
        </h2>
        <p className="text-gray-600">
          Système officiel de vérification d'authenticité des certificats de résidence
        </p>
      </div>

      {/* Bande tricolore */}
      <div className="flex h-2 rounded-full overflow-hidden mb-6 max-w-md mx-auto">
        <div className="flex-1 bg-red-600"></div>
        <div className="flex-1 bg-yellow-400"></div>
        <div className="flex-1 bg-green-600"></div>
      </div>
    </header>
  );
}
