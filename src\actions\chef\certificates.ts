"use server";

import { CERTIFICATE_STATUS } from "@/actions/auth/constants";
import { getCurrentUser } from "@/actions/auth/session";
import {
  AssignCertificateParams,
  Certificate,
  RejectCertificateParams,
  VerifyCertificateParams,
} from "@/actions/types";
import { createAdminClient } from "@/lib/server/appwrite";
import {
  AGENTS_COLLECTION_ID,
  CERTIFICATES_COLLECTION_ID,
  DATABASE_ID,
} from "@/lib/server/database";
import {
  DOCUMENTS_BUCKET_ID,
  SIGNATURES_BUCKET_ID,
} from "@/lib/server/storage";
import { PdfGenerator } from "@/lib/services/pdf-generator";
import { getAppwriteFileUrl } from "@/lib/utils/appwrite";
import { ID, Query } from "node-appwrite";

export async function assignCertificate({
  certificateId,
  agentId,
}: AssignCertificateParams) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = (await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    )) as Certificate;

    // Vérification des permissions
    if (certificate.chefId !== user.$id) {
      throw new Error("Vous n'avez pas les permissions nécessaires");
    }

    if (certificate.status !== CERTIFICATE_STATUS.SUBMITTED) {
      throw new Error(
        "Le certificat ne peut pas être assigné dans son état actuel"
      );
    }

    // Mise à jour du certificat
    const updatedCertificate = await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        status: CERTIFICATE_STATUS.PENDING,
        agentId,
        updatedAt: new Date().toISOString(),
        assignedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      certificate: updatedCertificate,
    };
  } catch (error) {
    console.error("Erreur lors de l'assignation du certificat:", error);
    throw error;
  }
}

export async function rejectCertificate({
  certificateId,
  reason,
}: RejectCertificateParams) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = (await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    )) as Certificate;

    // Vérification des permissions
    if (certificate.chefId !== user.$id) {
      throw new Error("Vous n'avez pas les permissions nécessaires");
    }

    // Vérification du statut
    if (
      ![CERTIFICATE_STATUS.SUBMITTED, CERTIFICATE_STATUS.PENDING].includes(
        certificate.status as CERTIFICATE_STATUS
      )
    ) {
      throw new Error(
        "Le certificat ne peut pas être rejeté dans son état actuel"
      );
    }

    // Mise à jour du certificat
    const updatedCertificate = await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        status: CERTIFICATE_STATUS.REJECTED,
        rejectedAt: new Date().toISOString(),
        rejectedBy: user.$id,
        rejectionReason: reason,
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      certificate: updatedCertificate,
    };
  } catch (error) {
    console.error("Erreur lors du rejet du certificat:", error);
    throw error;
  }
}

export async function verifyCertificate({
  certificateId,
  notes,
}: VerifyCertificateParams) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = (await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    )) as Certificate;

    // Vérification des permissions
    if (certificate.chefId !== user.$id) {
      throw new Error("Vous n'avez pas les permissions nécessaires");
    }

    if (
      ![CERTIFICATE_STATUS.PENDING, CERTIFICATE_STATUS.SUBMITTED].includes(
        certificate.status
      )
    ) {
      throw new Error(
        "Le certificat ne peut pas être vérifié dans son état actuel"
      );
    }

    // Mise à jour du certificat
    const updatedCertificate = await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        status: CERTIFICATE_STATUS.VERIFIED,
        verifiedAt: new Date().toISOString(),
        verifiedBy: user.$id,
        verificationNotes: notes || null,
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      certificate: updatedCertificate,
    };
  } catch (error) {
    console.error("Erreur lors de la vérification du certificat:", error);
    throw error;
  }
}

export async function signCertificate(
  certificateId: string,
  signatureFileId: string
) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    );

    // Vérification des permissions
    if (certificate.chefId !== user.$id && user.prefs?.role !== "admin") {
      throw new Error("Vous n'avez pas les permissions nécessaires");
    }

    if (certificate.status !== CERTIFICATE_STATUS.READY) {
      throw new Error("Le certificat n'est pas prêt pour la signature");
    }

    // Mise à jour du certificat
    const updatedCertificate = await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        status: CERTIFICATE_STATUS.SIGNED,
        signedAt: new Date().toISOString(),
        signedBy: user.$id,
        signatureFileId,
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      certificate: updatedCertificate,
    };
  } catch (error) {
    console.error("Erreur lors de la signature du certificat:", error);
    throw error;
  }
}

export async function deliverCertificate(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases, storage } = await createAdminClient();

    // Récupération du certificat avec toutes les informations nécessaires
    const certificate = (await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    )) as Certificate;

    // Vérification des permissions
    if (certificate.chefId !== user.$id && user.prefs?.role !== "admin") {
      throw new Error("Vous n'avez pas les permissions nécessaires");
    }

    // Vérification du statut
    if (certificate.status !== CERTIFICATE_STATUS.SIGNED) {
      throw new Error("Le certificat doit être signé avant d'être délivré");
    }

    // Génération du PDF
    const pdfBuffer = await PdfGenerator.generateCertificatePdf(certificateId);

    // Upload du PDF dans le bucket
    const pdfFile = await storage.createFile(
      DOCUMENTS_BUCKET_ID,
      ID.unique(),
      new File([pdfBuffer], `${certificate.reference}.pdf`, {
        type: "application/pdf",
      })
    );

    // Génération de l'URL complète du fichier
    const documentUrl = getAppwriteFileUrl(pdfFile.$id);

    // Mise à jour du certificat
    const updatedCertificate = await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        status: CERTIFICATE_STATUS.DELIVERED,
        deliveredAt: new Date().toISOString(),
        deliveredBy: user.$id,
        documentUrl,
        documentId: pdfFile.$id, // Stockage de l'ID du fichier pour référence future
        updatedAt: new Date().toISOString(),
      }
    );

    return {
      success: true,
      certificate: updatedCertificate,
    };
  } catch (error) {
    console.error("Erreur lors de la délivrance du certificat:", error);
    throw error;
  }
}

export async function unassignCertificate(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = (await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    )) as Certificate;

    // Vérification des permissions
    if (certificate.chefId !== user.$id) {
      throw new Error("Vous n'avez pas les permissions nécessaires");
    }

    if (!certificate.agentId) {
      throw new Error("Aucun agent n'est assigné à ce certificat");
    }

    // Mise à jour du certificat
    const updatedCertificate = await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        agentId: null,
        status: CERTIFICATE_STATUS.SUBMITTED,
        updatedAt: new Date().toISOString(),
        assignedAt: null,
      }
    );

    return {
      success: true,
      certificate: updatedCertificate,
    };
  } catch (error) {
    console.error("Erreur lors de la désassignation de l'agent:", error);
    throw error;
  }
}

export async function downloadCertificate(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = (await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    )) as Certificate;

    // Vérification du statut
    if (certificate.status !== CERTIFICATE_STATUS.DELIVERED) {
      throw new Error(
        "Le certificat n'est pas disponible pour le téléchargement"
      );
    }

    // Vérification si déjà téléchargé (sauf pour l'admin)
    if (certificate.downloadedAt && user.prefs.role !== "admin") {
      throw new Error("Ce certificat a déjà été téléchargé");
    }

    // Marquer comme téléchargé
    await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        downloadedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    );

    // TODO: Logique de téléchargement du fichier PDF
    // Retourner l'URL ou le blob du fichier

    return {
      success: true,
      // fileUrl: certificate.documentUrl,
    };
  } catch (error) {
    console.error("Erreur lors du téléchargement du certificat:", error);
    throw error;
  }
}

export async function getChefAgents() {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération des agents du chef
    const { documents: agents } = await databases.listDocuments(
      DATABASE_ID,
      AGENTS_COLLECTION_ID,
      [Query.equal("chefId", user.$id), Query.orderAsc("nom")]
    );

    return {
      success: true,
      agents: agents.map((agent) => ({
        id: agent.$id,
        nom: agent.nom,
        prenom: agent.prenom,
        email: agent.email,
        telephone: agent.telephone,
      })),
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des agents:", error);
    throw error;
  }
}

export async function downloadCertificatePDF(certificateId: string) {
  try {
    // Vérifier l'authentification et le rôle
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");
    if (user.prefs?.role !== "chef") throw new Error("Accès non autorisé");

    const { databases, storage } = await createAdminClient();

    // Récupérer le certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    );

    // Vérifier que le chef est bien assigné à ce certificat
    if (certificate.chefId !== user.$id) {
      throw new Error("Vous n'êtes pas autorisé à télécharger ce certificat");
    }

    // Vérifier que le certificat est dans un état téléchargeable
    if (certificate.status !== CERTIFICATE_STATUS.DELIVERED) {
      throw new Error(
        "Ce certificat n'est pas encore disponible au téléchargement"
      );
    }

    // Vérifier que le document généré existe
    if (!certificate.generatedDocumentId) {
      throw new Error("Le document du certificat n'a pas encore été généré");
    }

    try {
      // Télécharger le fichier depuis le storage
      const response = await storage.getFileDownload(
        DOCUMENTS_BUCKET_ID,
        certificate.generatedDocumentId
      );

      // Convertir l'ArrayBuffer en Blob avec le type MIME correct
      const blob = new Blob([response], { type: "application/pdf" });
      const url = URL.createObjectURL(blob);

      // Créer un lien de téléchargement avec le nom de fichier formaté
      const a = document.createElement("a");
      a.href = url;
      a.download = `certificat-${certificate.reference}-${
        new Date().toISOString().split("T")[0]
      }.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Mettre à jour les informations de téléchargement
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATES_COLLECTION_ID,
        certificateId,
        {
          downloadedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          downloads: (certificate.downloads || 0) + 1,
          lastDownloadedBy: user.$id,
        }
      );

      return { success: true };
    } catch (error) {
      console.error("Erreur lors du téléchargement du fichier:", error);
      throw new Error("Impossible de télécharger le fichier du certificat");
    }
  } catch (error: any) {
    console.error("Erreur lors du téléchargement du certificat:", error);
    throw new Error(
      error.message || "Une erreur est survenue lors du téléchargement"
    );
  }
}

export async function markCertificateAsReady(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    );

    // Vérification des permissions
    const isAuthorized =
      user.prefs?.role === "admin" ||
      certificate.chefId === user.$id ||
      certificate.agentId === user.$id;

    if (!isAuthorized) {
      throw new Error("Vous n'avez pas les permissions nécessaires");
    }

    // Vérification du statut
    if (certificate.status !== CERTIFICATE_STATUS.APPROVED) {
      if (certificate.status === CERTIFICATE_STATUS.VERIFIED) {
        // Mise à jour du certificat
        await databases.updateDocument(
          DATABASE_ID,
          CERTIFICATES_COLLECTION_ID,
          certificateId,
          {
            status: CERTIFICATE_STATUS.APPROVED,
            updatedAt: new Date().toISOString(),
            verifiedAt: certificate.verifiedAt || new Date().toISOString(),
            verifiedBy: certificate.verifiedBy || user.$id,
            verificationNotes:
              certificate.verificationNotes ||
              "Vérifié directement par le chef de quartier",
          }
        );
      } else {
        throw new Error(
          "Le certificat doit être approuvé avant d'être marqué comme prêt"
        );
      }
    }

    // Mise à jour du certificat
    const updatedCertificate = await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        status: CERTIFICATE_STATUS.READY,
        readyAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        readyBy: user.$id,
        verifiedAt: certificate.verifiedAt || new Date().toISOString(),
        verifiedBy: certificate.verifiedBy || user.$id,
        verificationNotes:
          certificate.verificationNotes ||
          "Vérifié directement par le chef de quartier",
      }
    );

    return {
      success: true,
      certificate: updatedCertificate,
    };
  } catch (error) {
    console.error("Erreur lors du marquage du certificat comme prêt:", error);
    throw error;
  }
}

export async function storeSignature(
  certificateId: string,
  signatureData: string
) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { storage } = await createAdminClient();

    // Convertir le base64 en Buffer
    const base64Data = signatureData.replace(/^data:image\/\w+;base64,/, "");
    const buffer = Buffer.from(base64Data, "base64");

    // Stocker la signature
    const signatureFile = await storage.createFile(
      SIGNATURES_BUCKET_ID,
      ID.unique(),
      new File([buffer], `signature-${certificateId}.png`, {
        type: "image/png",
      })
    );

    return {
      success: true,
      signatureFileId: signatureFile.$id,
    };
  } catch (error) {
    console.error("Erreur lors du stockage de la signature:", error);
    throw error;
  }
}
