# Générateur PDF Version 2 - Documentation

## Vue d'ensemble

Le nouveau générateur PDF Version 2 (`PdfGeneratorV2`) offre un design moderne et des fonctionnalités de sécurité améliorées pour les certificats de résidence guinéens, tout en conservant la compatibilité avec la version précédente.

## Architecture

### Classes disponibles

1. **`PdfGeneratorV1`** - Version originale (renommée pour compatibilité)
2. **`PdfGeneratorV2`** - Nouvelle version moderne avec design amélioré
3. **`PdfGenerator`** - Classe de compatibilité qui utilise V2 par défaut

## Nouvelles fonctionnalités V2

### 🎨 Design moderne

#### Bordures nationales guinéennes
- Bordure principale verte inspirée de l'identité nationale
- Bandes tricolores (Rouge, Jaune, Vert) en haut et en bas
- Design épuré et professionnel

#### Typographie améliorée
- Hiérarchie visuelle claire avec différentes tailles de police
- Couleurs cohérentes avec l'identité nationale
- Espacement optimisé pour une meilleure lisibilité

### 🔒 Sécurité renforcée

#### Éléments anti-falsification
- **Filigrane de sécurité** : ID citoyen en arrière-plan avec rotation 45°
- **Badge de sécurité** : Marquage "SÉCURISÉ GUINÉE 2024" en haut à droite
- **Hash de vérification** : Empreinte unique basée sur les données du certificat
- **Référence visible** : Numéro de référence affiché en haut à droite

#### ID Citoyen mis en valeur
- Encadré spécial avec bordure verte
- Police en gras et couleur distinctive
- Position centrale pour une identification rapide

### 📋 Informations conservées

Toutes les informations essentielles de la V1 sont préservées :
- Données personnelles du citoyen
- Informations de résidence
- Référence du certificat
- Signature et cachet du chef de quartier
- Validité du document (3 mois)

## Utilisation

### Migration automatique
```typescript
// L'interface reste identique - migration transparente
import { PdfGenerator } from "@/lib/services/pdf-generator";

const pdfBuffer = await PdfGenerator.generateCertificatePdf(certificateId);
// Utilise automatiquement la V2
```

### Utilisation spécifique d'une version
```typescript
import { PdfGeneratorV1, PdfGeneratorV2 } from "@/lib/services/pdf-generator";

// Version 1 (ancienne)
const pdfV1 = await PdfGeneratorV1.generateCertificatePdf(certificateId);

// Version 2 (moderne)
const pdfV2 = await PdfGeneratorV2.generateCertificatePdf(certificateId);
```

## Spécifications techniques

### Couleurs nationales
```typescript
const COLORS_V2 = {
  RED: "#CE1126",      // Rouge du drapeau guinéen
  YELLOW: "#FCD116",   // Jaune du drapeau guinéen
  GREEN: "#009639",    // Vert du drapeau guinéen
  DARK_GREEN: "#006B2F", // Vert foncé pour les titres
  LIGHT_GRAY: "#F8F9FA", // Gris clair pour les encadrés
  DARK_GRAY: "#343A40",  // Gris foncé pour le texte
};
```

### Dimensions et espacements
```typescript
const DESIGN_V2 = {
  PAGE_MARGIN: 15,        // Marge de page
  BORDER_WIDTH: 2,        // Épaisseur des bordures
  HEADER_HEIGHT: 80,      // Hauteur de l'en-tête
  FOOTER_HEIGHT: 60,      // Hauteur du pied de page
  CONTENT_PADDING: 20,    // Espacement du contenu
  SECURITY_OPACITY: 0.1,  // Opacité du filigrane
};
```

## Éléments de sécurité détaillés

### 1. Filigrane de sécurité
- **Contenu** : ID unique du citoyen
- **Position** : Centre de la page
- **Rotation** : 45 degrés
- **Opacité** : 10% pour ne pas gêner la lecture

### 2. Badge de sécurité
- **Position** : Coin supérieur droit
- **Contenu** : "SÉCURISÉ GUINÉE 2024"
- **Style** : Encadré avec bordure verte

### 3. Hash de vérification
- **Algorithme** : Hash simple basé sur les données du certificat
- **Affichage** : 8 caractères hexadécimaux
- **Position** : Sous la référence en haut à droite

### 4. ID Citoyen mis en valeur
- **Encadré** : Fond gris clair avec bordure verte
- **Police** : Gras, couleur vert foncé
- **Position** : Centre, sous l'en-tête

## Compatibilité

### Rétrocompatibilité
- ✅ Interface publique identique
- ✅ Même format de sortie (Buffer PDF)
- ✅ Mêmes paramètres d'entrée
- ✅ V1 toujours disponible si nécessaire

### Migration
- **Automatique** : `PdfGenerator` utilise V2 par défaut
- **Graduelle** : Possibilité de tester V2 avant migration complète
- **Réversible** : Retour à V1 possible si nécessaire

## Avantages de la V2

### Pour les utilisateurs
- 🎨 **Design moderne** et professionnel
- 🔒 **Sécurité renforcée** contre la falsification
- 👁️ **Lisibilité améliorée** avec une meilleure hiérarchie visuelle
- 🇬🇳 **Identité nationale** respectée avec les couleurs du drapeau

### Pour les développeurs
- 🔧 **Code modulaire** et maintenable
- 📚 **Documentation complète**
- 🧪 **Testabilité** améliorée
- 🔄 **Compatibilité** préservée

## Prochaines étapes

### Améliorations futures possibles
1. **QR Code** : Ajout d'un QR code pour vérification numérique
2. **Watermark avancé** : Motifs de sécurité plus sophistiqués
3. **Signature numérique** : Intégration de signatures cryptographiques
4. **Templates multiples** : Support de différents types de certificats

### Monitoring
- Suivi de l'adoption de la V2
- Feedback utilisateurs sur le nouveau design
- Métriques de performance comparées à la V1
