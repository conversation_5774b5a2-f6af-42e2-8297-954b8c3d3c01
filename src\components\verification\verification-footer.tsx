import Link from "next/link";
import { Shield, Globe, Phone, Mail } from "lucide-react";

export function VerificationFooter() {
  return (
    <footer className="mt-12 pt-8 border-t border-gray-200">
      <div className="max-w-4xl mx-auto">
        {/* Informations de sécurité */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center mb-4">
            <Shield className="w-6 h-6 text-green-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">
              Sécurité et Confidentialité
            </h3>
          </div>
          
          <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Protection des données</h4>
              <ul className="space-y-1">
                <li>• Aucune donnée personnelle n'est affichée</li>
                <li>• Seules les informations de validation sont visibles</li>
                <li>• Connexion sécurisée (HTTPS)</li>
                <li>• Respect de la confidentialité</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Authentification</h4>
              <ul className="space-y-1">
                <li>• Vérification cryptographique</li>
                <li>• Hash de sécurité unique</li>
                <li>• Horodatage certifié</li>
                <li>• Système anti-falsification</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Liens utiles */}
        <div className="grid md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="flex items-center mb-3">
              <Globe className="w-5 h-5 text-blue-600 mr-2" />
              <h4 className="font-semibold text-gray-800">Liens officiels</h4>
            </div>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/" className="text-blue-600 hover:text-blue-800">
                  Portail NCR
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-blue-600 hover:text-blue-800">
                  À propos du système
                </Link>
              </li>
              <li>
                <Link href="/help" className="text-blue-600 hover:text-blue-800">
                  Aide et support
                </Link>
              </li>
            </ul>
          </div>

          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="flex items-center mb-3">
              <Phone className="w-5 h-5 text-green-600 mr-2" />
              <h4 className="font-semibold text-gray-800">Contact</h4>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>Ligne d'assistance: +224 XXX XXX XXX</li>
              <li>Horaires: 8h-17h (Lun-Ven)</li>
              <li>Service gratuit</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="flex items-center mb-3">
              <Mail className="w-5 h-5 text-purple-600 mr-2" />
              <h4 className="font-semibold text-gray-800">Support technique</h4>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>Email: <EMAIL></li>
              <li>Signaler un problème</li>
              <li>Demande d'assistance</li>
            </ul>
          </div>
        </div>

        {/* Avertissement légal */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <h4 className="font-semibold text-yellow-800 mb-2">
            ⚠️ Avertissement Important
          </h4>
          <p className="text-sm text-yellow-700">
            Ce système de vérification est le seul moyen officiel de valider l'authenticité 
            des certificats de résidence de la République de Guinée. Tout certificat qui 
            n'apparaît pas comme valide dans ce système doit être considéré comme suspect. 
            En cas de doute, contactez immédiatement les autorités compétentes.
          </p>
        </div>

        {/* Copyright */}
        <div className="text-center py-4 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            © {new Date().getFullYear()} République de Guinée - Ministère de l'Administration du Territoire et de la Décentralisation
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Système de vérification officiel des certificats de résidence
          </p>
        </div>
      </div>
    </footer>
  );
}
