import { getCertificateWithFullData } from "@/actions/certificates";
import { Certificate } from "@/actions/types";
import { createAdminClient } from "@/lib/server/appwrite";
import { SIGNATURES_BUCKET_ID } from "@/lib/server/storage";
import { jsPDF } from "jspdf";

const COLORS = {
  RED: "#CE1126",
  YELLOW: "#FCD116",
  GREEN: "#009639",
};

const BORDER_WIDTH = 1;
const BORDER_SPACING = 1;
const PAGE_MARGIN = 10;
const CONTENT_MARGIN = PAGE_MARGIN + (BORDER_WIDTH + BORDER_SPACING) * 3;

export class PdfGenerator {
  private static baseUrl = process.env.NEXT_PUBLIC_APP_URL!;

  private static brandingBase64: string;
  private static armoirieBase64: string;
  private static simandouBase64: string;

  private static async loadImage(path: string): Promise<string> {
    const response = await fetch(path);
    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer).toString("base64");
  }

  static async generateCertificatePdf(certificateId: string): Promise<Buffer> {
    const { certificate } = await getCertificateWithFullData(certificateId);
    const { storage } = await createAdminClient();

    // Chargement de l'armoirie
    if (!this.armoirieBase64) {
      this.armoirieBase64 = await this.loadImage(
        `${this.baseUrl}/images/armoirie.png`
      );
    }

    // Chargement de la branding
    if (!this.brandingBase64) {
      this.brandingBase64 = await this.loadImage(
        `${this.baseUrl}/images/branding.png`
      );
    }

    // Chargement du logo de bas de page
    if (!this.simandouBase64) {
      this.simandouBase64 = await this.loadImage(
        `${this.baseUrl}/images/simandou.png`
      );
    }

    // Création du document PDF
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Ajout des bordures tricolores
    this.addTricolorBorder(doc);

    // Ajout de l'armoirie
    await this.addArmoirie(doc);

    // Ajout du filigrane
    await this.addWatermark(doc);

    // Ajout de l'en-tête
    this.addHeader(doc, certificate);

    // Ajout du contenu principal
    this.addMainContent(doc, certificate);

    // Avant l'ajout de la signature
    await this.addSimandou(doc);

    // Ajout de la signature si disponible
    if (certificate.signatureFileId) {
      try {
        const signatureFile = await storage.getFileDownload(
          SIGNATURES_BUCKET_ID,
          certificate.signatureFileId
        );
        await this.addSignature(doc, signatureFile, certificate);
      } catch (error) {
        console.error("Erreur lors de la récupération de la signature:", error);
      }
    }

    // Conversion en Buffer
    const pdfBuffer = Buffer.from(doc.output("arraybuffer"));
    return pdfBuffer;
  }

  private static addTricolorBorder(doc: jsPDF) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Bordure rouge (externe)
    doc.setDrawColor(COLORS.RED);
    doc.setLineWidth(BORDER_WIDTH);
    doc.rect(
      PAGE_MARGIN,
      PAGE_MARGIN,
      pageWidth - 2 * PAGE_MARGIN,
      pageHeight - 2 * PAGE_MARGIN
    );

    // Bordure jaune (milieu)
    doc.setDrawColor(COLORS.YELLOW);
    doc.rect(
      PAGE_MARGIN + BORDER_WIDTH + BORDER_SPACING,
      PAGE_MARGIN + BORDER_WIDTH + BORDER_SPACING,
      pageWidth - 2 * (PAGE_MARGIN + BORDER_WIDTH + BORDER_SPACING),
      pageHeight - 2 * (PAGE_MARGIN + BORDER_WIDTH + BORDER_SPACING)
    );

    // Bordure verte (interne)
    doc.setDrawColor(COLORS.GREEN);
    doc.rect(
      PAGE_MARGIN + 2 * (BORDER_WIDTH + BORDER_SPACING),
      PAGE_MARGIN + 2 * (BORDER_WIDTH + BORDER_SPACING),
      pageWidth - 2 * (PAGE_MARGIN + 2 * (BORDER_WIDTH + BORDER_SPACING)),
      pageHeight - 2 * (PAGE_MARGIN + 2 * (BORDER_WIDTH + BORDER_SPACING))
    );
  }

  private static async addArmoirie(doc: jsPDF) {
    const innerMargin = PAGE_MARGIN + 3 * (BORDER_WIDTH + BORDER_SPACING);
    const contentPadding = 7.5;

    doc.addImage(
      `data:image/png;base64,${this.armoirieBase64}`,
      "PNG",
      innerMargin + contentPadding,
      innerMargin + contentPadding,
      20,
      20
    );
  }

  private static async addWatermark(doc: jsPDF) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const watermarkSize = 140; // Taille du filigrane

    // Sauvegarde l'état graphique actuel
    doc.saveGraphicsState();

    // Calcul du centre de la page
    const centerX = pageWidth / 2;
    const centerY = pageHeight / 2;

    // Configuration de la transformation
    doc.setGState(doc.GState({ opacity: 0.2 }));

    // Ajoute l'image avec rotation
    doc.addImage(
      `data:image/png;base64,${this.brandingBase64}`,
      "PNG",
      centerX - watermarkSize / 5,
      centerY - watermarkSize / 3,
      watermarkSize,
      watermarkSize,
      undefined,
      undefined,
      30 // Rotation en degrés
    );

    // Restaure l'état graphique précédent (annule les transformations)
    doc.restoreGraphicsState();
  }

  private static addHeader(doc: jsPDF, certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const innerMargin = PAGE_MARGIN + 3 * (BORDER_WIDTH + BORDER_SPACING);
    const contentPadding = 12;
    let yPosition = innerMargin + 67.5;

    // En-tête
    doc.setFontSize(16);
    doc.setFont("helvetica", "bold");
    doc.text(
      "RÉPUBLIQUE DE GUINÉE",
      pageWidth / 2,
      innerMargin + contentPadding,
      {
        align: "center",
      }
    );

    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    doc.text(
      "Travail - Justice - Solidarité",
      pageWidth / 2,
      innerMargin + 18,
      { align: "center" }
    );

    // Ministère
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text(
      "MINISTÈRE DE L'ADMINISTRATION DU TERRITOIRE",
      pageWidth / 2,
      innerMargin + 30,
      { align: "center" }
    );
    doc.text("ET DE LA DÉCENTRALISATION", pageWidth / 2, innerMargin + 35.5, {
      align: "center",
    });

    // Informations administratives
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    const startY = innerMargin + 47.5;

    // Fonction helper pour dessiner les pointillés
    const drawDottedLine = (x: number, y: number, length: number) => {
      doc.saveGraphicsState();
      doc.setDrawColor(0, 0, 0);
      doc.setLineWidth(0.2);
      doc.setGState(doc.GState({ opacity: 0.5 }));
      doc.setLineDashPattern([0.5, 1], 0);
      doc.line(x, y, x + length, y);
      doc.restoreGraphicsState();
    };

    // Première ligne
    doc.text("RÉGION DE", innerMargin + contentPadding - 5, startY);
    const regionX = innerMargin + contentPadding + 30;
    drawDottedLine(regionX, startY, 40);
    if (certificate.quartier.region) {
      doc.text(certificate.quartier.region, regionX, startY);
    }

    doc.text(
      "PRÉFECTURE DE",
      pageWidth - (innerMargin + contentPadding) - 80,
      startY
    );
    const prefectureX = pageWidth - (innerMargin + contentPadding) - 20;
    drawDottedLine(prefectureX - 20, startY, 40);
    if (certificate.quartier.prefecture) {
      doc.text(certificate.quartier.prefecture, prefectureX - 5, startY, {
        align: "right",
      });
    }

    // Deuxième ligne
    doc.text("S/PRÉFECTURE DE", innerMargin + contentPadding - 5, startY + 8);
    const sprefectureX = innerMargin + contentPadding + 45;
    drawDottedLine(sprefectureX, startY + 8, 45);
    if (certificate.quartier.sousPrefecture) {
      doc.text(certificate.quartier.sousPrefecture, sprefectureX, startY + 8);
    }

    doc.text(
      "CR DE",
      pageWidth - (innerMargin + contentPadding) - 60,
      startY + 8
    );
    const crX = pageWidth - (innerMargin + contentPadding) - 20;
    drawDottedLine(crX - 20, startY + 8, 40);
    if (certificate.quartier.commune) {
      doc.text(certificate.quartier.commune, crX - 5, startY + 8, {
        align: "right",
      });
    }

    // Troisième ligne
    doc.text(
      "DISTRICT/QUARTIER DE",
      innerMargin + contentPadding - 5,
      startY + 16
    );
    const quartierX = innerMargin + contentPadding + 65;
    drawDottedLine(
      quartierX - 10,
      startY + 16,
      pageWidth - (2 * (innerMargin + contentPadding) + 55)
    );
    if (certificate.quartier.nom) {
      doc.text(certificate.quartier.nom, quartierX - 10, startY + 16);
    }
  }

  private static addMainContent(doc: jsPDF, certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const innerMargin = PAGE_MARGIN + 3 * (BORDER_WIDTH + BORDER_SPACING);
    const contentPadding = 7.5;
    let yPosition = innerMargin + 75;

    // Fonction helper pour dessiner les pointillés
    const drawDottedLine = (x: number, y: number, length: number) => {
      doc.saveGraphicsState();
      doc.setDrawColor(0, 0, 0);
      doc.setLineWidth(0.2);
      doc.setGState(doc.GState({ opacity: 0.5 }));
      doc.setLineDashPattern([0.5, 1], 0);
      doc.line(x, y, x + length, y);
      doc.restoreGraphicsState();
    };

    // Titre du certificat
    doc.setFontSize(16);
    doc.setFont("helvetica", "bold");
    doc.text(
      `CERTIFICAT DE RÉSIDENCE N°${certificate.reference}`,
      pageWidth / 2,
      yPosition,
      {
        align: "center",
      }
    );
    yPosition += 10;

    // Identifiant unique du citoyen - Section mise en évidence
    doc.setFontSize(10);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(100, 100, 100); // Gris foncé pour la discrétion
    doc.text(
      `ID CITOYEN: ${certificate.citizen.numeroIdentificationUnique}`,
      pageWidth / 2,
      yPosition,
      {
        align: "center",
      }
    );
    doc.setTextColor(0, 0, 0); // Retour au noir
    yPosition += 12;

    // Contenu principal
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");

    // En-tête du contenu
    doc.text("JE SOUSSIGNÉ M.", innerMargin + contentPadding, yPosition);
    const chefX = innerMargin + contentPadding + 60;
    drawDottedLine(chefX, yPosition, 100);
    if (certificate.chefQuartierName) {
      doc.text(certificate.chefQuartierName, chefX, yPosition);
    }
    yPosition += 8;

    doc.text(
      "PRÉSIDENT DU CONSEIL DE QUARTIER DE",
      innerMargin + contentPadding,
      yPosition
    );
    const quartierNameX = innerMargin + contentPadding + 95;
    drawDottedLine(quartierNameX, yPosition, 65);
    if (certificate.quartier.nom) {
      doc.text(certificate.quartier.nom, quartierNameX, yPosition);
    }
    yPosition += 8;
    doc.text("CERTIFIE QUE:", innerMargin + contentPadding, yPosition);
    yPosition += 8;

    // Informations du citoyen
    const infos = [
      { label: "M./MME", value: certificate.citizenName || "" },
      { label: "PROFESSION", value: certificate.citizen.profession || "" },
      {
        label: "NÉ(E) LE",
        value: new Date(
          certificate.citizen.dateNaissance || new Date()
        ).toLocaleDateString("fr-FR"),
        sameLine: true,
        nextLabel: "À",
        nextValue: certificate.citizen.lieuNaissance || "",
      },
      { label: "FILS/FILLE DE", value: certificate.citizen.nomPere || "" },
      { label: "ET DE", value: certificate.citizen.nomMere || "" },
      { label: "DE NATIONALITÉ", value: "Guinéenne" },
      {
        label: "RÉSIDE EFFECTIVEMENT",
        value: "",
        multiLine: true,
        nextLine: "CE QUARTIER DEPUIS LE",
        nextLineValue: new Date(
          certificate.citizen.dateInstallation || new Date()
        ).toLocaleDateString("fr-FR"),
      },
      {
        label: "N° CARTE ÉLECTORALE",
        value: certificate.citizen.carteElecteur || "",
      },
      { label: "MOTIF", value: certificate.motif || "" },
    ];

    infos.forEach(
      ({
        label,
        value,
        sameLine,
        nextLabel,
        nextValue,
        multiLine,
        nextLine,
        nextLineValue,
      }) => {
        if (multiLine) {
          // Première ligne
          doc.text(`${label}`, innerMargin + contentPadding, yPosition);
          yPosition += 8;

          // Deuxième ligne avec pointillés
          doc.text(`${nextLine}`, innerMargin + contentPadding, yPosition);
          const startX = innerMargin + contentPadding + 80; // Ajusté pour l'alignement
          drawDottedLine(startX - 20, yPosition, 80); // Augmenté la longueur pour plus d'espace
          if (nextLineValue) {
            doc.text(nextLineValue, startX - 20, yPosition);
          }
        } else if (sameLine) {
          // Premier label et valeur
          doc.text(`${label} :`, innerMargin + contentPadding, yPosition);
          const startX = innerMargin + contentPadding + 30;
          drawDottedLine(startX, yPosition, 40);
          if (value) {
            doc.text(value, startX, yPosition);
          }

          // Deuxième label et valeur sur la même ligne
          const secondLabelX = innerMargin + contentPadding + 90;
          doc.text(`${nextLabel} :`, secondLabelX, yPosition);
          const secondStartX = secondLabelX + 10;
          drawDottedLine(secondStartX, yPosition, 60);
          if (nextValue) {
            doc.text(nextValue, secondStartX, yPosition);
          }
        } else {
          // Cas standard
          doc.text(`${label} :`, innerMargin + contentPadding, yPosition);
          const startX = innerMargin + contentPadding + 60;
          drawDottedLine(startX, yPosition, 100);
          if (value) {
            doc.text(value, startX, yPosition);
          }
        }

        if (!multiLine || !sameLine) {
          yPosition += 8;
        }
      }
    );

    // Validité
    yPosition += 6;
    doc.text("Validité : 03 Mois", innerMargin + contentPadding, yPosition);

    // Formule de clôture
    yPosition += 10;
    doc.text(
      "En foi de quoi le présent certificat est délivré pour servir et valoir ce que de droit.",
      innerMargin + contentPadding,
      yPosition
    );

    // Date et lieu
    yPosition += 12;
    const dateDelivrance = certificate.deliveredAt
      ? new Date(certificate.deliveredAt).toLocaleDateString("fr-FR")
      : new Date().toLocaleDateString("fr-FR");

    doc.text(
      `Fait à ${certificate.quartier.commune}, le ${dateDelivrance}`,
      pageWidth - (innerMargin + contentPadding),
      yPosition,
      { align: "right" }
    );
  }

  private static async addSimandou(doc: jsPDF) {
    const pageHeight = doc.internal.pageSize.height;
    const innerMargin = PAGE_MARGIN + 3 * (BORDER_WIDTH + BORDER_SPACING);
    const yPosition = pageHeight - (innerMargin + 30);

    // Dimensions du logo Simandou
    const logoWidth = 60;
    const logoHeight = 30;

    doc.addImage(
      `data:image/png;base64,${this.simandouBase64}`,
      "PNG",
      innerMargin, // Aligné à gauche avec la marge intérieure
      yPosition - 5, // Position plus haute (-5mm) pour un meilleur alignement
      logoWidth,
      logoHeight
    );
  }

  private static async addSignature(
    doc: jsPDF,
    signatureBuffer: ArrayBuffer,
    certificate: Certificate
  ) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const innerMargin = PAGE_MARGIN + 3 * (BORDER_WIDTH + BORDER_SPACING);
    const contentPadding = 7.5;
    const yPosition = pageHeight - (innerMargin + 30);

    doc.setFontSize(12);
    doc.setFont("helvetica", "bold");
    doc.text(
      "Signature et Cachet du",
      pageWidth - (innerMargin + contentPadding),
      yPosition,
      {
        align: "right",
      }
    );
    doc.text(
      "Président du Conseil de Quartier / District",
      pageWidth - (innerMargin + contentPadding),
      yPosition + 8,
      { align: "right" }
    );

    const base64 = Buffer.from(signatureBuffer).toString("base64");
    doc.addImage(
      `data:image/png;base64,${base64}`,
      "PNG",
      pageWidth - (innerMargin + contentPadding) - 60,
      yPosition + 8,
      60,
      30
    );

    doc.text(
      certificate.chefQuartierName,
      pageWidth - (innerMargin + contentPadding),
      yPosition + 20,
      { align: "right" }
    );
  }
}
