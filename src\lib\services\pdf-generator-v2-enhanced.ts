import { getCertificateWithFullData } from "@/actions/certificates";
import { Certificate } from "@/actions/types";
import { CertificateVerificationService } from "@/lib/database/certificate-verification";
import { createAdminClient } from "@/lib/server/appwrite";
import { SIGNATURES_BUCKET_ID } from "@/lib/server/storage";
import {
  CertificateCrypto,
  CertificateHashData,
} from "@/lib/utils/certificate-crypto";
import { QRCodeGenerator } from "@/lib/utils/qr-generator";
import { jsPDF } from "jspdf";

const COLORS_V2 = {
  RED: "#CE1126",
  YELLOW: "#FCD116",
  GREEN: "#009639",
  DARK_GREEN: "#006B2F",
  LIGHT_GRAY: "#F8F9FA",
  DARK_GRAY: "#343A40",
  BLACK: "#000000",
  WHITE: "#FFFFFF",
};

const DESIGN_V2 = {
  PAGE_MARGIN: 15,
  BORDER_WIDTH: 2,
  HEADER_HEIGHT: 80,
  FOOTER_HEIGHT: 60,
  CONTENT_PADDING: 20,
  SECURITY_OPACITY: 0.1,
  QR_SIZE: 25,
  QR_MARGIN: 5,
};

export class PdfGeneratorV2Enhanced {
  private static baseUrl = process.env.NEXT_PUBLIC_APP_URL!;
  private static brandingBase64: string;
  private static armoirieBase64: string;
  private static simandouBase64: string;

  private static async loadImage(url: string): Promise<string> {
    try {
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString("base64");
      return base64;
    } catch (error) {
      console.error(`Erreur lors du chargement de l'image ${url}:`, error);
      return "";
    }
  }

  static async generateCertificatePdf(certificateId: string): Promise<Buffer> {
    const { certificate } = await getCertificateWithFullData(certificateId);
    const { storage } = await createAdminClient();

    // Chargement des images
    if (!this.armoirieBase64) {
      this.armoirieBase64 = await this.loadImage(
        `${this.baseUrl}/images/armoirie.png`
      );
    }

    if (!this.brandingBase64) {
      this.brandingBase64 = await this.loadImage(
        `${this.baseUrl}/images/branding.png`
      );
    }

    if (!this.simandouBase64) {
      this.simandouBase64 = await this.loadImage(
        `${this.baseUrl}/images/simandou.png`
      );
    }

    // Génération des données de sécurité
    const securityData = await this.generateSecurityData(certificate);

    // Création du document PDF
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Construction du document moderne avec sécurité
    this.addModernBorder(doc);
    this.addModernHeader(doc, certificate);
    this.addSecurityElements(doc, certificate, securityData);
    this.addModernContent(doc, certificate, securityData);
    await this.addQRCode(doc, securityData);
    this.addModernFooter(doc, certificate);

    // Ajout de la signature si disponible
    if (certificate.signatureFileId) {
      try {
        const signatureFile = await storage.getFileDownload(
          SIGNATURES_BUCKET_ID,
          certificate.signatureFileId
        );
        await this.addModernSignature(doc, signatureFile, certificate);
      } catch (error) {
        console.error("Erreur lors de la récupération de la signature:", error);
      }
    }

    return Buffer.from(doc.output("arraybuffer"));
  }

  private static async generateSecurityData(certificate: Certificate) {
    // Préparation des données pour le hash
    const hashData: CertificateHashData = {
      certificateId: certificate.$id,
      citizenId: certificate.citizenId,
      citizenIdUnique: certificate.citizen.numeroIdentificationUnique || "",
      timestamp: Date.now(),
      reference: certificate.reference,
      issuerInfo: {
        type: "chef",
        id: certificate.chefId,
        name: certificate.chefQuartierName || "",
      },
      locationInfo: {
        region: certificate.quartier?.region || "",
        commune: certificate.quartier?.commune || "",
        quartier: certificate.quartier?.nom || "",
      },
    };

    // Génération des données de sécurité
    const security = CertificateCrypto.generateCertificateSecurity(hashData);

    // Création de l'enregistrement de vérification en base
    try {
      const issuedAt = new Date();
      const expiresAt = new Date();
      expiresAt.setMonth(expiresAt.getMonth() + 3); // Validité 3 mois

      await CertificateVerificationService.createVerification({
        hash: security.verificationHash,
        certificateId: certificate.$id,
        citizenId: certificate.citizenId,
        issuedAt,
        expiresAt,
        metadata: {
          issuerType: hashData.issuerInfo.type,
          issuerId: hashData.issuerInfo.id,
          issuerName: hashData.issuerInfo.name,
          region: hashData.locationInfo.region,
          commune: hashData.locationInfo.commune,
          quartier: hashData.locationInfo.quartier,
        },
      });
    } catch (error) {
      console.error(
        "Erreur lors de la création de l'enregistrement de vérification:",
        error
      );
    }

    return security;
  }

  private static addModernBorder(doc: jsPDF) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN;
    const borderWidth = DESIGN_V2.BORDER_WIDTH;

    // Bordure principale verte
    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(borderWidth);
    doc.rect(margin, margin, pageWidth - 2 * margin, pageHeight - 2 * margin);

    // Bande tricolore en haut
    const bandHeight = 8;
    const bandWidth = pageWidth - 2 * margin - 2 * borderWidth;
    const bandX = margin + borderWidth;
    const bandY = margin + borderWidth;

    // Rouge
    doc.setFillColor(COLORS_V2.RED);
    doc.rect(bandX, bandY, bandWidth / 3, bandHeight, "F");

    // Jaune
    doc.setFillColor(COLORS_V2.YELLOW);
    doc.rect(bandX + bandWidth / 3, bandY, bandWidth / 3, bandHeight, "F");

    // Vert
    doc.setFillColor(COLORS_V2.GREEN);
    doc.rect(
      bandX + (2 * bandWidth) / 3,
      bandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );

    // Bande tricolore en bas
    const bottomBandY = pageHeight - margin - borderWidth - bandHeight;

    // Rouge
    doc.setFillColor(COLORS_V2.RED);
    doc.rect(bandX, bottomBandY, bandWidth / 3, bandHeight, "F");

    // Jaune
    doc.setFillColor(COLORS_V2.YELLOW);
    doc.rect(
      bandX + bandWidth / 3,
      bottomBandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );

    // Vert
    doc.setFillColor(COLORS_V2.GREEN);
    doc.rect(
      bandX + (2 * bandWidth) / 3,
      bottomBandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );
  }

  private static addModernHeader(doc: jsPDF, _certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const headerY = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH + 15;
    let currentY = headerY;

    // Armoirie au centre
    if (this.armoirieBase64) {
      const logoSize = 25;
      doc.addImage(
        `data:image/png;base64,${this.armoirieBase64}`,
        "PNG",
        pageWidth / 2 - logoSize / 2,
        currentY,
        logoSize,
        logoSize
      );
    }
    currentY += 35;

    // République de Guinée
    doc.setFontSize(14);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.DARK_GRAY);
    doc.text("RÉPUBLIQUE DE GUINÉE", pageWidth / 2, currentY, {
      align: "center",
    });
    currentY += 15;

    // Titre principal
    doc.setFontSize(24);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text("CERTIFICAT", pageWidth / 2, currentY, { align: "center" });
    currentY += 10;
    doc.text("DE RÉSIDENCE", pageWidth / 2, currentY, { align: "center" });
    currentY += 15;

    // Ligne de séparation
    doc.setDrawColor(COLORS_V2.DARK_GREEN);
    doc.setLineWidth(1);
    doc.line(pageWidth / 2 - 60, currentY, pageWidth / 2 + 60, currentY);
  }

  private static addSecurityElements(
    doc: jsPDF,
    certificate: Certificate,
    securityData: any
  ) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Filigrane de sécurité - Watermark dynamique en arrière-plan
    doc.saveGraphicsState();
    doc.setGState(doc.GState({ opacity: DESIGN_V2.SECURITY_OPACITY }));
    doc.setFontSize(40);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.GREEN);

    // Rotation du texte pour le filigrane
    doc.text(securityData.watermark, pageWidth / 2, pageHeight / 2, {
      align: "center",
      angle: 45,
    });
    doc.restoreGraphicsState();

    // Éléments de sécurité visibles
    this.addSecurityBadge(doc, certificate);
    this.addVerificationElements(doc, certificate, securityData);
  }

  private static addSecurityBadge(doc: jsPDF, _certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const badgeX = pageWidth - 60;
    const badgeY = 40;

    // Badge de sécurité
    doc.setFillColor(COLORS_V2.LIGHT_GRAY);
    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(1);
    doc.roundedRect(badgeX, badgeY, 45, 25, 3, 3, "FD");

    // Texte du badge
    doc.setFontSize(8);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text("SÉCURISÉ", badgeX + 22.5, badgeY + 8, { align: "center" });
    doc.text("GUINÉE", badgeX + 22.5, badgeY + 15, { align: "center" });
    doc.text("2024", badgeX + 22.5, badgeY + 22, { align: "center" });
  }

  private static addVerificationElements(
    doc: jsPDF,
    certificate: Certificate,
    securityData: any
  ) {
    // Référence du certificat en haut à droite
    const pageWidth = doc.internal.pageSize.width;
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.DARK_GRAY);
    doc.text(`Réf: ${certificate.reference}`, pageWidth - 20, 30, {
      align: "right",
    });

    // Hash de vérification
    doc.setFontSize(8);
    doc.text(`Hash: ${securityData.displayHash}`, pageWidth - 20, 35, {
      align: "right",
    });

    // Horodatage cryptographique
    doc.text(
      `TS: ${securityData.timestamp.timestampHash}`,
      pageWidth - 20,
      40,
      { align: "right" }
    );
  }

  private static addModernContent(
    doc: jsPDF,
    certificate: Certificate,
    securityData: any
  ) {
    let currentY = 120; // Position après l'en-tête

    // ID Citoyen mis en valeur
    this.addCitizenIdSection(doc, certificate, currentY);
    currentY += 25;

    // Informations principales
    this.addMainInformation(doc, certificate, currentY);
    currentY += 80;

    // Informations détaillées
    this.addDetailedInformation(doc, certificate, currentY);
  }

  private static addCitizenIdSection(
    doc: jsPDF,
    certificate: Certificate,
    yPosition: number
  ) {
    const pageWidth = doc.internal.pageSize.width;

    // Encadré pour l'ID citoyen
    const boxWidth = 160;
    const boxHeight = 20;
    const boxX = pageWidth / 2 - boxWidth / 2;

    doc.setFillColor(COLORS_V2.LIGHT_GRAY);
    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(1);
    doc.roundedRect(boxX, yPosition, boxWidth, boxHeight, 2, 2, "FD");

    // Texte ID Citoyen
    doc.setFontSize(12);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text(
      `ID CITOYEN: ${certificate.citizen.numeroIdentificationUnique}`,
      pageWidth / 2,
      yPosition + 12,
      { align: "center" }
    );
  }

  private static addMainInformation(
    doc: jsPDF,
    certificate: Certificate,
    yPosition: number
  ) {
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const contentX = margin + DESIGN_V2.CONTENT_PADDING;
    let currentY = yPosition;

    // Numéro de certificat
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text(`N° ${certificate.reference}`, contentX, currentY);
    currentY += 15;

    // Texte principal moderne
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.BLACK);

    const dateStr = new Date().toLocaleDateString("fr-FR");
    doc.text(`Le ${dateStr}`, contentX, currentY);
    currentY += 10;

    doc.text(
      `Certifie que ${certificate.citizenName || ""}`,
      contentX,
      currentY
    );
    currentY += 10;

    doc.text(
      `demeure à ${certificate.citizen.adressePrecise || ""}`,
      contentX,
      currentY
    );
    currentY += 10;

    doc.text(
      `en résidence dans la Commune de ${certificate.quartier?.commune || ""}`,
      contentX,
      currentY
    );
  }

  private static addDetailedInformation(
    doc: jsPDF,
    certificate: Certificate,
    yPosition: number
  ) {
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const contentX = margin + DESIGN_V2.CONTENT_PADDING;
    let currentY = yPosition;

    // Section des informations détaillées
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.DARK_GRAY);

    const infos = [
      { label: "Profession", value: certificate.citizen.profession || "" },
      {
        label: "Né(e) le",
        value: `${new Date(
          certificate.citizen.dateNaissance || new Date()
        ).toLocaleDateString("fr-FR")} à ${
          certificate.citizen.lieuNaissance || ""
        }`,
      },
      { label: "Fils/Fille de", value: certificate.citizen.nomPere || "" },
      { label: "Et de", value: certificate.citizen.nomMere || "" },
      { label: "Nationalité", value: "Guinéenne" },
      {
        label: "Installé(e) depuis le",
        value: new Date(
          certificate.citizen.dateInstallation || new Date()
        ).toLocaleDateString("fr-FR"),
      },
      {
        label: "N° Carte Électorale",
        value: certificate.citizen.carteElecteur || "",
      },
      { label: "Motif", value: certificate.motif || "" },
    ];

    infos.forEach((info) => {
      if (info.value) {
        doc.setFont("helvetica", "bold");
        doc.text(`${info.label}: `, contentX, currentY);

        doc.setFont("helvetica", "normal");
        const labelWidth = doc.getTextWidth(`${info.label}: `);
        doc.text(info.value, contentX + labelWidth, currentY);

        currentY += 8;
      }
    });

    // Validité
    currentY += 10;
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text("Validité: 03 Mois", contentX, currentY);

    // Formule de clôture
    currentY += 15;
    doc.setFont("helvetica", "italic");
    doc.setTextColor(COLORS_V2.BLACK);
    doc.text(
      "En foi de quoi le présent certificat est délivré pour servir et valoir ce que de droit.",
      contentX,
      currentY
    );
  }

  private static async addQRCode(doc: jsPDF, securityData: any) {
    try {
      // Génération du QR code
      const qrCodeBase64 = await QRCodeGenerator.generateCertificateQR(
        securityData.verificationUrl,
        securityData.verificationHash
      );

      // Position du QR code (coin inférieur gauche)
      const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
      const qrX = margin + DESIGN_V2.QR_MARGIN;
      const qrY =
        doc.internal.pageSize.height -
        margin -
        DESIGN_V2.QR_SIZE -
        DESIGN_V2.QR_MARGIN;

      // Ajout du QR code
      doc.addImage(
        `data:image/png;base64,${qrCodeBase64}`,
        "PNG",
        qrX,
        qrY,
        DESIGN_V2.QR_SIZE,
        DESIGN_V2.QR_SIZE
      );

      // Texte explicatif sous le QR code
      doc.setFontSize(8);
      doc.setFont("helvetica", "normal");
      doc.setTextColor(COLORS_V2.DARK_GRAY);
      doc.text(
        "Scanner pour vérifier",
        qrX + DESIGN_V2.QR_SIZE / 2,
        qrY + DESIGN_V2.QR_SIZE + 5,
        { align: "center" }
      );
    } catch (error) {
      console.error("Erreur lors de l'ajout du QR code:", error);
      // Continuer sans QR code en cas d'erreur
    }
  }

  private static addModernFooter(doc: jsPDF, certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const footerY = pageHeight - margin - DESIGN_V2.FOOTER_HEIGHT;

    // Date et lieu
    const dateDelivrance = certificate.deliveredAt
      ? new Date(certificate.deliveredAt).toLocaleDateString("fr-FR")
      : new Date().toLocaleDateString("fr-FR");

    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.BLACK);
    doc.text(
      `Fait à ${certificate.quartier?.commune || ""}, le ${dateDelivrance}`,
      pageWidth - margin - DESIGN_V2.CONTENT_PADDING,
      footerY + 20,
      { align: "right" }
    );

    // Logo Simandou en bas à droite (décalé pour laisser place au QR code)
    if (this.simandouBase64) {
      doc.addImage(
        `data:image/png;base64,${this.simandouBase64}`,
        "PNG",
        pageWidth - margin - 70,
        footerY + 10,
        50,
        25
      );
    }
  }

  private static async addModernSignature(
    doc: jsPDF,
    signatureBuffer: ArrayBuffer,
    certificate: Certificate
  ) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const signatureY = pageHeight - margin - DESIGN_V2.FOOTER_HEIGHT + 30;

    // Encadré pour la signature
    const signatureBoxWidth = 120;
    const signatureBoxHeight = 60;
    const signatureBoxX =
      pageWidth - margin - DESIGN_V2.CONTENT_PADDING - signatureBoxWidth;

    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(1);
    doc.rect(signatureBoxX, signatureY, signatureBoxWidth, signatureBoxHeight);

    // Titre de la signature
    doc.setFontSize(10);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text(
      "Signature et Cachet du",
      signatureBoxX + signatureBoxWidth / 2,
      signatureY - 5,
      { align: "center" }
    );
    doc.text(
      "Président du Conseil de Quartier",
      signatureBoxX + signatureBoxWidth / 2,
      signatureY + 3,
      { align: "center" }
    );

    // Image de la signature
    const base64 = Buffer.from(signatureBuffer).toString("base64");
    doc.addImage(
      `data:image/png;base64,${base64}`,
      "PNG",
      signatureBoxX + 10,
      signatureY + 10,
      signatureBoxWidth - 20,
      30
    );

    // Nom du signataire
    doc.setFontSize(9);
    doc.setFont("helvetica", "normal");
    doc.text(
      certificate.chefQuartierName || "",
      signatureBoxX + signatureBoxWidth / 2,
      signatureY + signatureBoxHeight - 5,
      { align: "center" }
    );
  }
}
