import { APP_URL } from "./env";

export const PAYMENT_CONFIG = {
  orangeMoney: {
    baseUrl: process.env.ORANGE_MONEY_API_URL || "https://api.orange.com",
    merchantKey: process.env.ORANGE_MONEY_MERCHANT_KEY || "", // Changé en string
    clientId: process.env.ORANGE_MONEY_CLIENT_ID || "",
    clientSecret: process.env.ORANGE_MONEY_CLIENT_SECRET || "",
    authorizationHeader: process.env.ORANGE_MONEY_AUTHORIZATION_HEADER || "",
    currency: "OUV",
    environment: (process.env.ORANGE_MONEY_ENVIRONMENT || "dev") as
      | "dev"
      | "prod",
    webhookSecret: process.env.ORANGE_MONEY_WEBHOOK_SECRET || "",
    returnUrl: `${APP_URL}/payment/return`,
    cancelUrl: `${APP_URL}/payment/cancel`,
    notificationUrl: `${APP_URL}/api/webhooks/orange-money`,
    lang: "fr",
  },
} as const;

export type PaymentProvider = "orange-money" | "wave" | "moov-money";

export interface PaymentTransaction {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  provider: PaymentProvider;
  paymentUrl?: string;
  payToken?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export enum PaymentStatus {
  INITIATED = "INITIATED", // user has clicked on “Confirmer”, the transaction is in progress on Orange side
  PENDING = "PENDING", //  waiting for user entry
  EXPIRED = "EXPIRED", // user has clicked on “Confirmer” too late (after token’s validity)
  SUCCESS = "SUCCESS", // payment is done
  FAILED = "FAILED", // payment has failed
}

export interface PaymentMetadata {
  certificateId: string;
  type: "certificate_download";
  returnUrl: string;
}
