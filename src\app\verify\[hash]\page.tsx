import { Metadata } from "next";
import { notFound } from "next/navigation";
import { verifyCertificate, VerificationResult } from "@/actions/certificate-verification";
import { VerificationDisplay } from "@/components/verification/verification-display";
import { VerificationHeader } from "@/components/verification/verification-header";
import { VerificationFooter } from "@/components/verification/verification-footer";

interface VerifyPageProps {
  params: {
    hash: string;
  };
  searchParams: {
    debug?: string;
    cert?: string;
    ts?: string;
  };
}

export async function generateMetadata({ params }: VerifyPageProps): Promise<Metadata> {
  const { hash } = params;
  
  return {
    title: `Vérification de Certificat - ${hash.substring(0, 8)}...`,
    description: "Vérification d'authenticité d'un certificat de résidence de la République de Guinée",
    robots: "noindex, nofollow", // Éviter l'indexation des pages de vérification
  };
}

export default async function VerifyPage({ params, searchParams }: VerifyPageProps) {
  const { hash } = params;
  const { debug, cert, ts } = searchParams;

  // Validation basique du format du hash
  if (!hash || hash.length !== 32 || !/^[a-f0-9]+$/i.test(hash)) {
    notFound();
  }

  // Vérification du certificat
  let verificationResult: VerificationResult;
  
  try {
    verificationResult = await verifyCertificate(hash);
  } catch (error) {
    console.error("Erreur lors de la vérification:", error);
    verificationResult = {
      isValid: false,
      status: 'not_found',
      message: 'Erreur lors de la vérification du certificat'
    };
  }

  // Informations de debug (si activées)
  const debugInfo = debug === 'true' ? {
    hash,
    certificateId: cert,
    timestamp: ts,
    verificationTime: new Date().toISOString(),
  } : undefined;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-yellow-50">
      <div className="container mx-auto px-4 py-8">
        {/* En-tête */}
        <VerificationHeader />

        {/* Contenu principal */}
        <div className="max-w-4xl mx-auto">
          <VerificationDisplay 
            result={verificationResult}
            hash={hash}
            debugInfo={debugInfo}
          />
        </div>

        {/* Pied de page */}
        <VerificationFooter />
      </div>
    </div>
  );
}
