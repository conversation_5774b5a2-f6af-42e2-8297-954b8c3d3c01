"use server";

import { CERTIFICATE_STATUS, CERTIFICATE_TYPE } from "@/actions/auth/constants";
import { getCurrentUser } from "@/actions/auth/session";
import { createAdminClient } from "@/lib/server/appwrite";
import {
  AGENTS_COLLECTION_ID,
  CERTIFICATES_COLLECTION_ID,
  CHEFS_COLLECTION_ID,
  CITIZENS_COLLECTION_ID,
  DATABASE_ID,
  DOCUMENTS_COLLECTION_ID,
  QUARTIERS_COLLECTION_ID,
} from "@/lib/server/database";
import { DOCUMENTS_BUCKET_ID } from "@/lib/server/storage";
import { generateCertificateReference } from "@/lib/utils/reference";
import { AttachedDocumentStatus } from "@/schemas/citizen";
import { ID, Query } from "node-appwrite";
import { Certificate, CertificateDocument } from "../types";

type RequestCertificateParams = {
  motif: string;
  files: File[];
  quartier: string;
};

export async function createCertificateRequest({
  motif,
  files,
  quartier,
}: RequestCertificateParams) {
  try {
    // 1. Vérification de l'authentification
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    // 2. Initialisation des clients Appwrite
    const { databases, storage } = await createAdminClient();

    // 3. Récupération du chef de quartier
    const { documents: quartiers } = await databases.listDocuments(
      DATABASE_ID,
      QUARTIERS_COLLECTION_ID,
      [Query.equal("nom", quartier), Query.limit(1)]
    );

    if (quartiers.length === 0) {
      throw new Error("Quartier non trouvé");
    }

    const chefId = quartiers[0].chefId;
    const region = quartiers[0].region;
    const commune = quartiers[0].commune;
    if (!chefId) {
      throw new Error("Ce quartier n'a pas de chef assigné");
    }

    // 4. Création du certificat
    const certificate = await databases.createDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      ID.unique(),
      {
        reference: generateCertificateReference(region, commune),
        citizenId: user.$id,
        chefId,
        status: CERTIFICATE_STATUS.SUBMITTED,
        motif,
        type: CERTIFICATE_TYPE.RESIDENCE,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    );

    // 5. Upload et enregistrement des fichiers si présents
    if (files.length > 0) {
      const documentIds = await Promise.all(
        files.map(async (file) => {
          // Upload du fichier dans le bucket
          const uploadedFile = await storage.createFile(
            DOCUMENTS_BUCKET_ID,
            ID.unique(),
            file
          );

          // Création du document dans la collection documents
          const document = await databases.createDocument(
            DATABASE_ID,
            DOCUMENTS_COLLECTION_ID,
            ID.unique(),
            {
              type: "complément de documents (demande de certificat)",
              fileId: uploadedFile.$id,
              fileName: file.name,
              fileSize: file.size.toString(),
              mimeType: file.type,
              status: AttachedDocumentStatus.PENDING,
            }
          );

          return document.$id;
        })
      );

      // Mise à jour du certificat avec les IDs des documents
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATES_COLLECTION_ID,
        certificate.$id,
        {
          documents: documentIds,
          updatedAt: new Date().toISOString(),
        }
      );
    }

    return { success: true, certificate };
  } catch (error) {
    console.error("Erreur lors de la création de la demande:", error);
    throw error;
  }
}

export async function getCertificateById(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = (await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    )) as Certificate;

    // Vérification des permissions selon le rôle
    const userRole = user.prefs?.role;
    const hasAccess =
      userRole === "admin" || // Admin a accès à tout
      (userRole === "chef" && certificate.chefId === user.$id) || // Chef de quartier uniquement à ses certificats
      (userRole === "agent" && certificate.agentId === user.$id) || // Agent uniquement aux certificats assignés
      (userRole === "citizen" && certificate.citizenId === user.$id); // Citoyen uniquement à ses certificats

    if (!hasAccess) {
      throw new Error("Non autorisé à accéder à ce certificat");
    }

    // Récupération des informations associées en parallèle
    const [citizensData, chefQuartierData, agentData] = await Promise.all([
      // Récupération des informations du citoyen
      databases.listDocuments(DATABASE_ID, CITIZENS_COLLECTION_ID, [
        Query.equal("userId", certificate.citizenId),
        Query.limit(1),
      ]),
      // Récupération des informations du chef de quartier
      databases.listDocuments(DATABASE_ID, CHEFS_COLLECTION_ID, [
        Query.equal("userId", certificate.chefId),
        Query.limit(1),
      ]),
      // Récupération des informations de l'agent si assigné
      certificate.agentId
        ? databases.getDocument(
            DATABASE_ID,
            AGENTS_COLLECTION_ID,
            certificate.agentId
          )
        : Promise.resolve(null),
    ]);

    const citizenDoc = citizensData.documents[0];
    const chefDoc = chefQuartierData.documents[0];
    const agentDoc = agentData;

    const rejectedByName = agentDoc
      ? `${agentDoc.prenom} ${agentDoc.nom}`
      : chefDoc
      ? `${chefDoc.prenom} ${chefDoc.nom}`
      : null;

    // Enrichissement du certificat avec les informations associées
    const enrichedCertificate = {
      ...certificate,
      documents: certificate.documents || ([] as CertificateDocument[]),
      citizen: citizenDoc,
      citizenName: citizenDoc
        ? `${citizenDoc.prenom} ${citizenDoc.nom}`
        : "N/A",
      chefQuartier: chefDoc,
      chefQuartierName: chefDoc ? `${chefDoc.prenom} ${chefDoc.nom}` : "N/A",
      agent: agentDoc,
      agentName: agentDoc ? `${agentDoc.prenom} ${agentDoc.nom}` : null,
      rejectedByName,
    };

    return {
      success: true,
      certificate: enrichedCertificate,
    };
  } catch (error) {
    console.error("Erreur lors de la récupération du certificat:", error);
    throw error;
  }
}

export async function getUserCertificates(
  filters: {
    status?: CERTIFICATE_STATUS;
    limit?: number;
    offset?: number;
  } = {}
) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Construction des queries
    const queries = [Query.equal("userId", user.$id)];

    if (filters.status) {
      queries.push(Query.equal("status", filters.status));
    }

    if (filters.limit) {
      queries.push(Query.limit(filters.limit));
    }

    if (filters.offset) {
      queries.push(Query.offset(filters.offset));
    }

    // Récupération des certificats
    const { documents, total } = await databases.listDocuments(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      queries
    );

    return {
      success: true,
      certificates: documents,
      pagination: {
        total,
        limit: filters.limit || 10,
        offset: filters.offset || 0,
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des certificats:", error);
    throw error;
  }
}

export async function getPendingCertificates(params: {
  limit?: number;
  offset?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Statuts considérés comme "en attente"
    const pendingStatuses = [
      CERTIFICATE_STATUS.SUBMITTED,
      CERTIFICATE_STATUS.PENDING,
      CERTIFICATE_STATUS.VERIFIED,
      CERTIFICATE_STATUS.APPROVED,
      CERTIFICATE_STATUS.READY,
      CERTIFICATE_STATUS.SIGNED,
    ];

    // Construction des queries
    const queries: any[] = [Query.equal("chefId", user.$id)];

    // Ajout du filtre de statut
    queries.push(Query.equal("status", pendingStatuses));

    // Ajout de la recherche si présente
    if (params.search) {
      queries.push(Query.search("reference", params.search));
    }

    // Ajout du tri
    if (params.sortBy) {
      if (params.sortOrder === "asc") {
        queries.push(Query.orderAsc(params.sortBy));
      } else {
        queries.push(Query.orderDesc(params.sortBy));
      }
    } else {
      // Tri par défaut sur la date de création
      queries.push(Query.orderDesc("createdAt"));
    }

    // Ajout de la pagination
    if (params.limit) {
      queries.push(Query.limit(params.limit));
    }

    if (params.offset) {
      queries.push(Query.offset(params.offset));
    }

    // Récupération des certificats
    const { documents, total } = await databases.listDocuments(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      queries
    );

    // Récupérer tous les citizenIds uniques
    const citizenIds = [...new Set(documents.map((doc) => doc.citizenId))];
    // Récupérer tous les chefIds uniques
    const chefIds = [...new Set(documents.map((doc) => doc.chefId))];

    // Récupérer les informations en parallèle
    const [citizensData, quartiersData] = await Promise.all([
      // Récupération des citoyens
      citizenIds.length > 0
        ? databases.listDocuments(DATABASE_ID, CITIZENS_COLLECTION_ID, [
            Query.equal("userId", citizenIds),
          ])
        : { documents: [] },
      // Récupération des quartiers
      chefIds.length > 0
        ? databases.listDocuments(DATABASE_ID, QUARTIERS_COLLECTION_ID, [
            Query.equal("chefId", chefIds),
          ])
        : { documents: [] },
    ]);

    // Créer les maps pour un accès rapide aux données
    const citizensMap = new Map(
      citizensData.documents.map((citizen) => [citizen.userId, citizen])
    );
    const quartiersMap = new Map(
      quartiersData.documents.map((quartier) => [quartier.chefId, quartier])
    );

    // Joindre les données
    const certificatesWithData = documents.map((doc) => {
      const citizen = citizensMap.get(doc.citizenId);
      const quartier = quartiersMap.get(doc.chefId);

      return {
        ...doc,
        citizen,
        citizenName: citizen ? `${citizen.prenom} ${citizen.nom}` : "N/A",
        quartier,
        quartierInfo: quartier
          ? `${quartier.nom} (${quartier.commune})`
          : "N/A",
      };
    });

    return {
      success: true,
      certificates: certificatesWithData,
      pagination: {
        total,
        limit: params.limit || 10,
        offset: params.offset || 0,
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des certificats:", error);
    throw error;
  }
}

export async function uploadCertificateDocument({
  certificateId,
  file,
}: {
  certificateId: string;
  file: File;
}) {
  try {
    const { storage } = await createAdminClient();
    const fileId = ID.unique();
    await storage.createFile(DOCUMENTS_BUCKET_ID, fileId, file);

    // Mettre à jour le certificat avec le nouveau document
    const { databases } = await createAdminClient();
    await databases.createDocument(
      DATABASE_ID,
      DOCUMENTS_COLLECTION_ID,
      ID.unique(),
      {
        certificateId,
        fileId,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        status: "pending",
        createdAt: new Date().toISOString(),
      }
    );

    return true;
  } catch (error) {
    console.error("Erreur lors de l'upload du document:", error);
    throw error;
  }
}
